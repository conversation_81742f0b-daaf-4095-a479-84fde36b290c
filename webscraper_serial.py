#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اسکریپت استخراج داده‌های سریال‌ها از سایت myf2m.net
نویسنده: AI Assistant
تاریخ: 2025-07-23
"""

import os
import re
import csv
import json
import time
import hashlib
from pathlib import Path
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import logging
from datetime import datetime

class SeriesScraper:
    """کلاس اصلی برای استخراج داده‌های سریال‌ها"""

    def __init__(self, series_folder='myf2m_series'):
        """مقداردهی اولیه اسکریپت"""
        self.series_folder = Path(series_folder)
        self.base_url = 'https://www.myf2m.net'

        # فایل‌های CSV خروجی
        self.series_info_file = 'series_info.csv'
        self.series_episodes_file = 'series_episodes.csv'
        self.series_details_file = 'series_details.csv'
        self.processed_series_file = 'processed_series.json'

        # آمار پردازش
        self.stats = {
            'total_series': 0,
            'processed_series': 0,
            'new_episodes': 0,
            'errors': 0,
            'start_time': datetime.now()
        }

        # تنظیم لاگ
        self.setup_logging()

        # بارگذاری سریال‌های پردازش شده
        self.processed_series = self.load_processed_series()

        # مقداردهی فایل‌های CSV
        self.initialize_csv_files()

    def setup_logging(self):
        """تنظیم سیستم لاگ‌گیری"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('series_scraper.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def load_processed_series(self):
        """بارگذاری لیست سریال‌های پردازش شده"""
        if os.path.exists(self.processed_series_file):
            try:
                with open(self.processed_series_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.logger.info(f"📂 بارگذاری {len(data)} سریال پردازش شده")
                    return data
            except Exception as e:
                self.logger.error(f"خطا در بارگذاری فایل پردازش شده: {e}")
        return {}

    def save_processed_series(self):
        """ذخیره لیست سریال‌های پردازش شده"""
        try:
            with open(self.processed_series_file, 'w', encoding='utf-8') as f:
                json.dump(self.processed_series, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"خطا در ذخیره فایل پردازش شده: {e}")

    def generate_series_id(self, series_name, url):
        """تولید ID یکتا برای سریال"""
        unique_string = f"{series_name}_{url}"
        return hashlib.md5(unique_string.encode('utf-8')).hexdigest()[:12]

    def initialize_csv_files(self):
        """ایجاد فایل‌های CSV اگر وجود نداشته باشند"""

        # فایل اطلاعات پایه سریال‌ها
        if not os.path.exists(self.series_info_file):
            with open(self.series_info_file, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                writer.writerow([
                    'series_id', 'persian_name', 'english_name', 'release_date',
                    'rating', 'poster_url', 'url', 'date_added'
                ])

        # فایل قسمت‌ها و لینک‌های دانلود
        if not os.path.exists(self.series_episodes_file):
            with open(self.series_episodes_file, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                writer.writerow([
                    'series_id', 'season_number', 'episode_number', 'episode_title',
                    'download_link', 'quality', 'file_size', 'episode_date', 'link_type'
                ])

        # فایل جزئیات سریال‌ها
        if not os.path.exists(self.series_details_file):
            with open(self.series_details_file, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                writer.writerow([
                    'series_id', 'plot_summary', 'genres', 'total_seasons',
                    'total_episodes', 'series_status', 'cast_info', 'director',
                    'country', 'language', 'duration'
                ])

    def find_series_files(self):
        """پیدا کردن تمام فایل‌های HTML سریال‌ها"""
        series_files = []
        series_path = self.series_folder / 'www.myf2m.net' / 'series'

        if not series_path.exists():
            self.logger.error(f"پوشه سریال‌ها یافت نشد: {series_path}")
            return []

        for series_dir in series_path.iterdir():
            if series_dir.is_dir():
                index_file = series_dir / 'index.html'
                if index_file.exists():
                    series_files.append(index_file)

        self.logger.info(f"🎬 {len(series_files)} سریال یافت شد")
        return series_files

    def extract_series_name(self, soup, file_path):
        """استخراج نام سریال (فارسی و انگلیسی)"""
        try:
            # استخراج از title
            title_elem = soup.find('title')
            if title_elem:
                title_text = title_elem.get_text().strip()

                # حذف کلمات اضافی
                title_text = re.sub(r'دانلود سریال\s*', '', title_text)
                title_text = re.sub(r'\s*بدون سانسور.*', '', title_text)

                # جدا کردن نام فارسی و انگلیسی
                persian_name = ''
                english_name = ''

                # الگوی معمول: "نام فارسی English Name سال"
                match = re.search(r'(.+?)\s+([A-Za-z\s]+)\s*(\d{4})?', title_text)
                if match:
                    persian_part = match.group(1).strip()
                    english_part = match.group(2).strip()

                    # تشخیص کدام قسمت فارسی و کدام انگلیسی است
                    if re.search(r'[آ-ی]', persian_part):
                        persian_name = persian_part
                        english_name = english_part
                    else:
                        english_name = persian_part
                        persian_name = english_part

            # استخراج از h1
            if not persian_name and not english_name:
                h1_elem = soup.find('h1', class_='entry-title')
                if h1_elem:
                    title_text = h1_elem.get_text().strip()
                    # تکرار همان فرآیند
                    match = re.search(r'(.+?)\s+([A-Za-z\s]+)\s*(\d{4})?', title_text)
                    if match:
                        persian_part = match.group(1).strip()
                        english_part = match.group(2).strip()

                        if re.search(r'[آ-ی]', persian_part):
                            persian_name = persian_part
                            english_name = english_part
                        else:
                            english_name = persian_part
                            persian_name = english_part

            return persian_name, english_name

        except Exception as e:
            self.logger.error(f"خطا در استخراج نام سریال: {e}")
            return '', ''

    def extract_release_date(self, soup):
        """استخراج تاریخ انتشار"""
        try:
            # جستجو در متن برای سال
            text_content = soup.get_text()
            year_matches = re.findall(r'\b(19|20)\d{2}\b', text_content)

            if year_matches:
                # انتخاب احتمالی‌ترین سال
                years = [int(year) for year in year_matches]
                current_year = datetime.now().year
                valid_years = [year for year in years if 1950 <= year <= current_year + 2]

                if valid_years:
                    return str(max(valid_years))  # جدیدترین سال

            return ''

        except Exception as e:
            self.logger.error(f"خطا در استخراج تاریخ انتشار: {e}")
            return ''

    def extract_rating(self, soup):
        """استخراج امتیاز IMDB"""
        try:
            # جستجو برای امتیاز IMDB
            imdb_elements = soup.find_all('svg')
            for svg in imdb_elements:
                use_elem = svg.find('use')
                if use_elem and '#icon-imdb' in str(use_elem.get('xlink:href', '')):
                    # پیدا کردن عدد امتیاز در نزدیکی
                    parent = svg.find_parent()
                    if parent:
                        rating_text = parent.get_text()
                        rating_match = re.search(r'(\d+\.?\d*)/10', rating_text)
                        if rating_match:
                            return rating_match.group(1)

            return ''

        except Exception as e:
            self.logger.error(f"خطا در استخراج امتیاز: {e}")
            return ''

    def extract_poster_url(self, soup):
        """استخراج URL پوستر"""
        try:
            # جستجو در meta tags
            og_image = soup.find('meta', property='og:image')
            if og_image:
                return og_image.get('content', '')

            # جستجو در تصاویر اصلی
            poster_img = soup.find('img', class_='wp-post-image')
            if poster_img:
                return poster_img.get('src', '')

            return ''

        except Exception as e:
            self.logger.error(f"خطا در استخراج پوستر: {e}")
            return ''

    def extract_genres(self, soup):
        """استخراج ژانرهای سریال"""
        try:
            genres = []

            # جستجو در entry-genres
            genre_div = soup.find('div', class_='entry-genres')
            if genre_div:
                genre_links = genre_div.find_all('a')
                for link in genre_links:
                    genre_text = link.get_text().strip()
                    if genre_text and genre_text not in ['ژانرها', ':']:
                        genres.append(genre_text)

            return ', '.join(genres) if genres else ''

        except Exception as e:
            self.logger.error(f"خطا در استخراج ژانر: {e}")
            return ''

    def extract_plot_summary(self, soup):
        """استخراج خلاصه داستان"""
        try:
            # جستجو در entry-excerpt
            excerpt_div = soup.find('div', class_='entry-excerpt')
            if excerpt_div:
                summary_text = excerpt_div.get_text().strip()
                # حذف کاراکترهای اضافی
                summary_text = re.sub(r'\s+', ' ', summary_text)
                return summary_text

            return ''

        except Exception as e:
            self.logger.error(f"خطا در استخراج خلاصه: {e}")
            return ''

    def extract_cast_info(self, soup):
        """استخراج اطلاعات بازیگران"""
        try:
            cast_info = {}

            # جستجو برای کارگردان
            director_elem = soup.find(text=re.compile(r'کارگردان'))
            if director_elem:
                parent = director_elem.find_parent()
                if parent:
                    directors = []
                    for link in parent.find_all('a'):
                        directors.append(link.get_text().strip())
                    cast_info['director'] = ', '.join(directors)

            # جستجو برای ستارگان
            stars_elem = soup.find(text=re.compile(r'ستارگان'))
            if stars_elem:
                parent = stars_elem.find_parent()
                if parent:
                    stars = []
                    for link in parent.find_all('a'):
                        stars.append(link.get_text().strip())
                    cast_info['stars'] = ', '.join(stars)

            return cast_info

        except Exception as e:
            self.logger.error(f"خطا در استخراج اطلاعات بازیگران: {e}")
            return {}

    def extract_series_details(self, soup):
        """استخراج جزئیات اضافی سریال"""
        try:
            details = {}

            # استخراج کشور سازنده
            country_elem = soup.find(text=re.compile(r'کشور سازنده'))
            if country_elem:
                parent = country_elem.find_parent()
                if parent:
                    country_link = parent.find('a')
                    if country_link:
                        details['country'] = country_link.get_text().strip()

            # استخراج زبان
            language_elem = soup.find(text=re.compile(r'زبان'))
            if language_elem:
                parent = language_elem.find_parent()
                if parent:
                    languages = []
                    for link in parent.find_all('a'):
                        languages.append(link.get_text().strip())
                    details['language'] = ', '.join(languages)

            # استخراج مدت زمان
            duration_elem = soup.find(text=re.compile(r'زمان'))
            if duration_elem:
                parent = duration_elem.find_parent()
                if parent:
                    duration_text = parent.get_text()
                    duration_match = re.search(r'(\d+)\s*دقیقه', duration_text)
                    if duration_match:
                        details['duration'] = duration_match.group(1) + ' دقیقه'

            return details

        except Exception as e:
            self.logger.error(f"خطا در استخراج جزئیات سریال: {e}")
            return {}

    def extract_seasons_and_episodes(self, soup):
        """استخراج اطلاعات فصل‌ها و قسمت‌ها"""
        try:
            seasons_data = []

            # جستجو برای فصل‌ها
            season_divs = soup.find_all('div', class_='download-season')

            for season_div in season_divs:
                season_info = {}

                # استخراج نام فصل
                season_button = season_div.find('button')
                if season_button:
                    season_text = season_button.get_text().strip()

                    # استخراج شماره فصل
                    season_match = re.search(r'فصل\s*(\w+)', season_text)
                    if season_match:
                        season_number_text = season_match.group(1)
                        # تبدیل کلمات فارسی به عدد
                        season_number = self.persian_word_to_number(season_number_text)
                        season_info['season_number'] = season_number
                        season_info['season_name'] = season_text

                        # استخراج وضعیت فصل
                        status_span = season_button.find('span')
                        if status_span:
                            season_info['status'] = status_span.get_text().strip()

                # استخراج قسمت‌ها
                episodes = []
                download_lists = season_div.find_all('div', class_='download-list')

                for download_list in download_lists:
                    # تشخیص نوع (دوبله یا زیرنویس)
                    list_type = 'subtitle'  # پیش‌فرض
                    if 'dubbled' in download_list.get('class', []):
                        list_type = 'dubbed'
                    elif download_list.find('p', class_='title'):
                        title_text = download_list.find('p', class_='title').get_text()
                        if 'دوبله' in title_text:
                            list_type = 'dubbed'

                    # استخراج کیفیت‌ها
                    quality_items = download_list.find_all('li', class_='bg-body')

                    for quality_item in quality_items:
                        quality_info = {}

                        # استخراج کیفیت
                        quality_span = quality_item.find('span', class_='text')
                        if quality_span:
                            quality_info['quality'] = quality_span.get_text().strip()

                        # استخراج تعداد قسمت‌ها
                        episode_count_elem = quality_item.find(text=re.compile(r'تعداد قسمت‌ها'))
                        if episode_count_elem:
                            parent = episode_count_elem.find_parent()
                            if parent:
                                count_text = parent.get_text()
                                count_match = re.search(r'(\d+)', count_text)
                                if count_match:
                                    quality_info['episode_count'] = int(count_match.group(1))

                        # استخراج لینک‌های دانلود
                        download_items_div = quality_item.find('div', class_='series-downloaditems')
                        if download_items_div:
                            episode_links = []
                            download_divs = download_items_div.find_all('div', class_='d-flex')

                            for i, download_div in enumerate(download_divs, 1):
                                episode_link = {}

                                # استخراج لینک دانلود
                                download_link = download_div.find('a', class_='btn-default')
                                if download_link:
                                    episode_link['download_url'] = download_link.get('href', '')
                                    episode_link['episode_title'] = download_link.get_text().strip()
                                    episode_link['episode_number'] = i
                                    episode_link['quality'] = quality_info.get('quality', '')
                                    episode_link['link_type'] = list_type

                                    episode_links.append(episode_link)

                            quality_info['episodes'] = episode_links

                        episodes.append(quality_info)

                season_info['episodes'] = episodes
                seasons_data.append(season_info)

            return seasons_data

        except Exception as e:
            self.logger.error(f"خطا در استخراج فصل‌ها و قسمت‌ها: {e}")
            return []

    def persian_word_to_number(self, word):
        """تبدیل کلمات فارسی اعداد به عدد"""
        persian_numbers = {
            'اول': 1, 'یکم': 1, 'یک': 1,
            'دوم': 2, 'دو': 2,
            'سوم': 3, 'سه': 3,
            'چهارم': 4, 'چهار': 4,
            'پنجم': 5, 'پنج': 5,
            'ششم': 6, 'شش': 6,
            'هفتم': 7, 'هفت': 7,
            'هشتم': 8, 'هشت': 8,
            'نهم': 9, 'نه': 9,
            'دهم': 10, 'ده': 10
        }

        word = word.strip()
        if word in persian_numbers:
            return persian_numbers[word]

        # اگر عدد باشد
        if word.isdigit():
            return int(word)

        return 1  # پیش‌فرض

    def process_series_file(self, file_path):
        """پردازش یک فایل سریال"""
        try:
            self.logger.info(f"🔄 پردازش: {file_path.name}")

            # خواندن فایل HTML
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            soup = BeautifulSoup(content, 'html.parser')

            # استخراج اطلاعات پایه
            persian_name, english_name = self.extract_series_name(soup, file_path)
            release_date = self.extract_release_date(soup)
            rating = self.extract_rating(soup)
            poster_url = self.extract_poster_url(soup)

            # تولید ID یکتا
            series_id = self.generate_series_id(english_name or persian_name, str(file_path))

            # بررسی آیا قبلاً پردازش شده
            if series_id in self.processed_series:
                existing_data = self.processed_series[series_id]
                # بررسی برای به‌روزرسانی تدریجی
                return self.check_for_updates(series_id, soup, existing_data)

            # استخراج جزئیات
            genres = self.extract_genres(soup)
            plot_summary = self.extract_plot_summary(soup)
            cast_info = self.extract_cast_info(soup)
            series_details = self.extract_series_details(soup)
            seasons_data = self.extract_seasons_and_episodes(soup)

            # ذخیره در فایل series_info.csv
            self.save_series_info(series_id, persian_name, english_name,
                                release_date, rating, poster_url, str(file_path))

            # ذخیره در فایل series_details.csv
            self.save_series_details(series_id, plot_summary, genres, seasons_data,
                                   cast_info, series_details)

            # ذخیره قسمت‌ها در فایل series_episodes.csv
            self.save_episodes_data(series_id, seasons_data)

            # ذخیره در فایل پردازش شده
            self.processed_series[series_id] = {
                'persian_name': persian_name,
                'english_name': english_name,
                'last_processed': datetime.now().isoformat(),
                'total_seasons': len(seasons_data),
                'file_path': str(file_path)
            }

            self.stats['processed_series'] += 1
            self.logger.info(f"✅ {persian_name or english_name} پردازش شد")

            return True

        except Exception as e:
            self.logger.error(f"❌ خطا در پردازش {file_path}: {e}")
            self.stats['errors'] += 1
            return False

    def check_for_updates(self, series_id, soup, existing_data):
        """بررسی برای به‌روزرسانی تدریجی"""
        try:
            # استخراج داده‌های جدید
            seasons_data = self.extract_seasons_and_episodes(soup)

            # مقایسه با داده‌های موجود
            current_seasons = existing_data.get('total_seasons', 0)
            new_seasons = len(seasons_data)

            if new_seasons > current_seasons:
                self.logger.info(f"🔄 به‌روزرسانی {existing_data.get('persian_name', 'نامشخص')}")

                # ذخیره قسمت‌های جدید
                self.save_episodes_data(series_id, seasons_data, incremental=True)

                # به‌روزرسانی اطلاعات
                existing_data['total_seasons'] = new_seasons
                existing_data['last_processed'] = datetime.now().isoformat()

                self.stats['new_episodes'] += 1
                return True

            return False

        except Exception as e:
            self.logger.error(f"خطا در بررسی به‌روزرسانی: {e}")
            return False

    def save_series_info(self, series_id, persian_name, english_name,
                        release_date, rating, poster_url, url):
        """ذخیره اطلاعات پایه سریال"""
        try:
            with open(self.series_info_file, 'a', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                writer.writerow([
                    series_id, persian_name, english_name, release_date,
                    rating, poster_url, url, datetime.now().isoformat()
                ])
        except Exception as e:
            self.logger.error(f"خطا در ذخیره اطلاعات پایه: {e}")

    def save_series_details(self, series_id, plot_summary, genres, seasons_data,
                           cast_info, series_details):
        """ذخیره جزئیات سریال"""
        try:
            # محاسبه آمار
            total_seasons = len(seasons_data)
            total_episodes = 0

            for season in seasons_data:
                for episode_group in season.get('episodes', []):
                    total_episodes += len(episode_group.get('episodes', []))

            # تشخیص وضعیت سریال
            series_status = 'ongoing'  # پیش‌فرض
            for season in seasons_data:
                if 'کامل' in season.get('status', ''):
                    series_status = 'completed'
                    break

            with open(self.series_details_file, 'a', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                writer.writerow([
                    series_id, plot_summary, genres, total_seasons,
                    total_episodes, series_status,
                    cast_info.get('stars', ''), cast_info.get('director', ''),
                    series_details.get('country', ''),
                    series_details.get('language', ''),
                    series_details.get('duration', '')
                ])
        except Exception as e:
            self.logger.error(f"خطا در ذخیره جزئیات: {e}")

    def save_episodes_data(self, series_id, seasons_data, incremental=False):
        """ذخیره داده‌های قسمت‌ها"""
        try:
            # در حالت تدریجی، ابتدا داده‌های موجود را بخوانیم
            existing_episodes = set()
            if incremental and os.path.exists(self.series_episodes_file):
                with open(self.series_episodes_file, 'r', encoding='utf-8-sig') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        if row['series_id'] == series_id:
                            episode_key = f"{row['season_number']}_{row['episode_number']}_{row['quality']}"
                            existing_episodes.add(episode_key)

            with open(self.series_episodes_file, 'a', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)

                for season in seasons_data:
                    season_number = season.get('season_number', 1)

                    for episode_group in season.get('episodes', []):
                        for episode in episode_group.get('episodes', []):
                            episode_key = f"{season_number}_{episode['episode_number']}_{episode['quality']}"

                            # در حالت تدریجی، قسمت‌های موجود را رد کن
                            if incremental and episode_key in existing_episodes:
                                continue

                            writer.writerow([
                                series_id, season_number, episode['episode_number'],
                                episode['episode_title'], episode['download_url'],
                                episode['quality'], '', '', episode['link_type']
                            ])

        except Exception as e:
            self.logger.error(f"خطا در ذخیره قسمت‌ها: {e}")

    def run_full_scrape(self):
        """اجرای اسکرپ کامل"""
        self.logger.info("🚀 شروع اسکرپ کامل سریال‌ها")

        # پیدا کردن فایل‌های سریال
        series_files = self.find_series_files()
        self.stats['total_series'] = len(series_files)

        if not series_files:
            self.logger.warning("هیچ فایل سریالی یافت نشد!")
            return

        # پردازش هر فایل
        for i, file_path in enumerate(series_files, 1):
            self.logger.info(f"📊 پیشرفت: {i}/{len(series_files)}")
            self.process_series_file(file_path)

            # ذخیره پیشرفت هر 10 سریال
            if i % 10 == 0:
                self.save_processed_series()

            # استراحت کوتاه
            time.sleep(0.1)

        # ذخیره نهایی
        self.save_processed_series()
        self.print_final_stats()

    def run_incremental_update(self):
        """اجرای به‌روزرسانی تدریجی"""
        self.logger.info("🔄 شروع به‌روزرسانی تدریجی")

        series_files = self.find_series_files()
        updated_count = 0

        for file_path in series_files:
            # فقط سریال‌های پردازش شده را بررسی کن
            temp_id = self.generate_series_id('temp', str(file_path))
            if temp_id in self.processed_series:
                if self.process_series_file(file_path):
                    updated_count += 1

        self.save_processed_series()
        self.logger.info(f"✅ {updated_count} سریال به‌روزرسانی شد")

    def print_final_stats(self):
        """نمایش آمار نهایی"""
        duration = datetime.now() - self.stats['start_time']

        print("\n" + "="*50)
        print("📊 آمار نهایی اسکرپ سریال‌ها")
        print("="*50)
        print(f"🎬 تعداد کل سریال‌ها: {self.stats['total_series']}")
        print(f"✅ سریال‌های پردازش شده: {self.stats['processed_series']}")
        print(f"🆕 قسمت‌های جدید: {self.stats['new_episodes']}")
        print(f"❌ خطاها: {self.stats['errors']}")
        print(f"⏱️ مدت زمان: {duration}")
        print("="*50)


def main():
    """تابع اصلی برنامه"""
    import argparse

    parser = argparse.ArgumentParser(description='اسکریپت استخراج داده‌های سریال‌ها')
    parser.add_argument('--mode', choices=['full', 'incremental', 'test'],
                       default='full', help='حالت اجرا')
    parser.add_argument('--folder', default='myf2m_series',
                       help='پوشه حاوی فایل‌های سریال')
    parser.add_argument('--test-count', type=int, default=5,
                       help='تعداد سریال‌ها برای تست')

    args = parser.parse_args()

    # ایجاد اسکریپر
    scraper = SeriesScraper(args.folder)

    if args.mode == 'full':
        scraper.run_full_scrape()
    elif args.mode == 'incremental':
        scraper.run_incremental_update()
    elif args.mode == 'test':
        scraper.run_test_scrape(args.test_count)

    print("\n🎉 اسکرپ با موفقیت تمام شد!")
    print(f"📁 فایل‌های خروجی:")
    print(f"   - {scraper.series_info_file}")
    print(f"   - {scraper.series_episodes_file}")
    print(f"   - {scraper.series_details_file}")


# اضافه کردن متد تست به کلاس
def add_test_method():
    """اضافه کردن متد تست به کلاس"""
    def run_test_scrape(self, test_count=5):
        """اجرای تست روی تعداد محدودی سریال"""
        self.logger.info(f"🧪 شروع تست روی {test_count} سریال")

        series_files = self.find_series_files()[:test_count]
        self.stats['total_series'] = len(series_files)

        for i, file_path in enumerate(series_files, 1):
            self.logger.info(f"🧪 تست {i}/{len(series_files)}: {file_path.name}")
            self.process_series_file(file_path)

        self.save_processed_series()
        self.print_final_stats()

    # اضافه کردن متد به کلاس
    SeriesScraper.run_test_scrape = run_test_scrape

# اجرای اضافه کردن متد
add_test_method()


if __name__ == "__main__":
    main()