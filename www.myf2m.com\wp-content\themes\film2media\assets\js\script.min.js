const themejs=document.getElementById("themejs"),ajaxURL=themejs.dataset.ajax,validationForms=document.querySelectorAll(".form-validation");Array.from(validationForms).forEach(function(e){e.addEventListener("submit",function(t){e.checkValidity()||(t.preventDefault(),t.stopPropagation()),e.classList.add("was-validated")},!1);const t=e.querySelectorAll("input, textarea, select");Array.from(t).forEach(function(e){e.oninvalid=function(e){e.target.setCustomValidity(""),!e.target.validity.valid&&e.target.title.length>0&&e.target.setCustomValidity(e.target.title)},e.oninput=function(e){e.target.setCustomValidity("")}})}),document.addEventListener("DOMContentLoaded",function(){if("undefined"!=typeof Splide&&document.querySelector(".splide")){const t=document.querySelectorAll(".splide");for(var e=0;e<t.length;e++)void 0!==t[e].dataset.splide&&null!==t[e].dataset.splide&&new Splide(t[e]).mount();const s=document.querySelectorAll(".posts-splide.splide");for(e=0;e<s.length;e++)new Splide(s[e],{gap:16,arrows:!0,fixedWidth:180,pagination:!1,direction:"rtl",lazyLoad:"sequential",breakpoints:{640:{gap:10,arrows:!1,fixedWidth:170}}}).mount()}if(document.getElementById("cover-suggested-movies")){const e=new Splide("#cover-suggested-movies",{rewind:!0,arrows:!1,direction:"rtl",pagination:!1,lazyLoad:"nearby",drag:!1,breakpoints:{640:{destroy:!0}}}),t=new Splide("#suggested-thumbnails",{autoWidth:160,gap:20,autoplay:!0,direction:"rtl",pagination:!1,arrows:!1,isNavigation:!0,lazyLoad:"sequential",breakpoints:{640:{perPage:3,isNavigation:!1}}});e.sync(t),e.mount(),t.mount()}}),"undefined"!=typeof jQuery&&(jQuery(document).ready(function(e){if(e(document).on("mouseenter",".megasub .navs .list-group",function(){var t=e(this).attr("ref");e(this).addClass("active").siblings().removeClass("active"),e(t).show().siblings().hide()}),e(document).on("click",".btn-searchtoggle",function(t){t.preventDefault(),e("#filter-results").slideToggle(function(){e(this).addClass("opened")})}),e("#filter-results .btn-close").click(function(){e("#filter-results").removeClass("opened").slideUp()}),e(document).click(function(t){0===e(t.target).parents("#filter-results").length&&e("#filter-results").hasClass("opened")&&e("#filter-results").removeClass("opened").hide()}),e("#notificationsList").on("show.bs.collapse",function(t){e.post(ajaxURL,{action:"notices_user_set_last_read"})}),"function"==typeof e.fn.select2&&e("select.select2").select2(),e("#series-table-play").length>0){const s=new Splide("#series-table-play .splide",{gap:16,arrows:!1,fixedWidth:210,pagination:!1,direction:"rtl",breakpoints:{640:{gap:10,fixedWidth:170}}});function t(t){e.ajax({type:"GET",url:ajaxURL,data:{action:"f2m_series_playtable",day:t},beforeSend:function(){e("#series-table-play .splide").html('<div class="splide__track"><div class="posts splide__list"><div class="splide__slide"><div class="entry skeleton"><div class="entry-cover"></div><div style="height: 20px;"></div></div></div><div class="splide__slide"><div class="entry skeleton"><div class="entry-cover"></div><div style="height: 20px;"></div></div></div><div class="splide__slide"><div class="entry skeleton"><div class="entry-cover"></div><div style="height: 20px;"></div></div></div><div class="splide__slide"><div class="entry skeleton"><div class="entry-cover"></div><div style="height: 20px;"></div></div></div></div></div>'),s.refresh()},success:function(t){e("#series-table-play .splide").html(t),s.refresh()}})}if(s.mount(),document.documentElement.clientWidth>992){const s=e("#series-table-play-day select").find("option"),a=e("#series-table-play-day select").val();e("#series-table-play-day select").remove(),e.each(s,function(t,s){e("#series-table-play-day").append(`<button class='btn btn-day fsz-14 btn-default' value="${e(s).val()}">${e(s).text()}</button>`)}),e(`#series-table-play-day .btn[value=${a}]`).removeClass("btn-default").addClass("btn-primary"),e(document).on("click","#series-table-play-day .btn",function(s){e(this).removeClass("btn-default").addClass("btn-primary").siblings().addClass("btn-default").removeClass("btn-primary"),t(e(this).val())})}else e(document).on("change","#series-table-play-day select",function(s){t(e(this).val())})}let s;if(e("#trailerModal").on("hide.bs.modal",function(t){const s=e(this).find("video");e(s).trigger("pause")}).on("shown.bs.modal",function(){const t=e(this).find("video");e(t).trigger("play")}),e(document).on("click",".btn-open-folat-search",function(t){t.preventDefault(),e(".float-search").slideToggle()}),e(document).on("click",".btn-share-page",function(e){if(e.preventDefault(),navigator.share)navigator.share({title:this.dataset.title||document.title,url:this.dataset.url||document.URL});else if(document.getElementById("shareModal")){const e=new bootstrap.Modal("#shareModal");e.show()}else console.error("Share Modal not found")}),e(document).on("click",".bulkcopy_series > .btn",function(t){const s=e(this).parent().find("textarea");s.select(),navigator.clipboard.writeText(s.val());const a=e('<span class="tooltip">لینک ها کپی شد</span>').insertAfter(s);setTimeout(function(){a.fadeOut(function(){e(this).remove()})},1500)}),e(document).on("input",".float-search input",function(t){const a=e(this).parents(".float-search"),n=e(this).val();a.find(".icon").html(""),e("body").find(':input[name="s"]').val(n).change(),clearTimeout(s),0==n.length?e("#saerch-ajaxresult").hide().html(""):n.length>2&&(a.find(".icon").html('<i class="spinner-grow text-primary" role="status" style="--spiner_size:20px;"></i>'),s=setTimeout(function(){e.ajax({type:"GET",url:ajaxURL,data:{action:"f2m_search_content",query:n},success:function(t){a.find(".icon").html(""),e("#saerch-ajaxresult").html(t).fadeIn()}})},500))}),document.getElementById("result_years")){const t=e("#result_years");t.find(".slider-theme").slider({range:!0,min:t.data("min"),max:t.data("max"),values:[t.find('[name="min_year"]').val(),t.find('[name="max_year"]').val()],slide:function(e,s){t.find('[name="min_year"]').val(s.values[0]),t.find('[name="max_year"]').val(s.values[1]),t.find(".from").text(s.values[0]),t.find(".to").text(s.values[1])}})}e(document).on("change","#filter-posts select",function(t){t.preventDefault(),console.log(e(this).val())}),e(document).on("change","#dashboard-nav-select",function(t){t.preventDefault(),window.location.href=e(this).val()}),e(document).on("click","[data-watchlist]",function(t){t.preventDefault();const s=e(this);s.html('<span class="spinner-border" style="border-width:2px" role="status"><span class="sr-only">Loading...</span></span>'),e.post(ajaxURL,{action:"user_watchlist",post_id:s.data("watchlist")},function(e){"added"==e.response?s.attr("title","حذف از لیست تماشا").html('<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><use xlink:href="#icon-minus" /></svg>'):s.attr("title","افزودن به لیست تماشا").html('<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><use xlink:href="#icon-plus" /></svg>')})}),e(document).on("click",".like-dislike .btn",function(t){t.preventDefault();const s=e(this);s.parents(".like-dislike").find(".btn").removeClass("filled").find(".count").html("&hellip;"),e.post(ajaxURL,{action:"post_comment_user_feedback",reaction:s.attr("name"),target:s.val(),type:s.attr("ref")},function(t){if(!t.status)return!1;t.fill&&s.addClass("filled"),s.parents(".like-dislike").find('[name="like"] .count').html(t.like),s.parents(".like-dislike").find('[name="dislike"] .count').html(t.dislike),e(".feedback-totalcount").html(t.total),e(".feedback-satisfaction").html(`${t.satisfaction}%`)})}),e(document).on("click",".comment-text.spoiled",function(t){t.preventDefault(),e(this).removeClass("spoiled")}),e("[data-countdown]").each(function(t,s){if(void 0!==s.dataset.countdown&&null!==s.dataset.countdown){const t=new Date(s.dataset.countdown).getTime(),a=setInterval(function(){let n=(new Date).getTime(),i=t-n,l=Math.floor(i/864e5),o=Math.floor(i%864e5/36e5),d=Math.floor(i%36e5/6e4),r=Math.floor(i%6e4/1e3);e(s).find(".dd").html(l.toLocaleString(void 0,{minimumIntegerDigits:2})),e(s).find(".hh").html(o.toLocaleString(void 0,{minimumIntegerDigits:2})),e(s).find(".mm").html(d.toLocaleString(void 0,{minimumIntegerDigits:2})),e(s).find(".ss").html(r.toLocaleString(void 0,{minimumIntegerDigits:2})),l<=0&&e(s).find(".dd").hide(),i<=0&&(clearInterval(a),e(s).html("&mdash;"))},1e3)}}),e("body").on("keyup","input[name=signup_password], input[name=signup_password_confirm]",function(){if(""===e("input[name=signup_password]").val())e("#password-strength").attr("class","");else{const t=wp.passwordStrength.meter(e("input[name=signup_password]").val(),[],e("input[name=signup_password_confirm]").val());switch(t){case 2:e("#password-strength").attr("class","").addClass("text-danger");break;case 3:e("#password-strength").attr("class","").addClass("text-warning");break;case 4:e("#password-strength").attr("class","").addClass("text-success");break;case 5:e("#password-strength").attr("class","").addClass("text-danger");break;default:e("#password-strength").attr("class","").addClass("text-muted")}}})}),$("#reportModal form").on("submit",function(e){e.preventDefault();const t=$(this);$.ajax({type:"POST",url:ajaxURL,data:{action:"f2m_report_problem",inputs:t.serialize()},beforeSend:function(){t.find(":input").prop("disabled",!0),$("#reportmodalmessage").html('<div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div>')},success:function(e){t.find(":input").prop("disabled",!1),$("#reportmodalmessage").html(`<p class="alert alert-${e.status?"success":"danger"}" role="alert">${e.response}</p>`)}})}));