#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from webscraper_serial_online import OnlineSeriesScraper

def test_duplicate_check():
    """تست بررسی تکراری بودن سریال‌ها"""
    scraper = OnlineSeriesScraper()
    
    print("🔍 تست سیستم بررسی تکراری")
    print("="*50)
    
    # بررسی بارگذاری فایل‌ها
    print(f"📂 تعداد سریال‌های پردازش شده: {len(scraper.processed_series)}")
    print(f"📂 تعداد URL های اسکرپ شده: {len(scraper.scraped_urls)}")
    
    # تست یک سریال موجود
    test_series_name = "دانلود سریال The Bear فصل 4 بدون سانسور با زیرنویس فارسی چسبیده"
    test_series_url = "https://www.myf2m.com/series/the-bear-series/"
    
    series_id = scraper.generate_series_id(test_series_name, test_series_url)
    print(f"\n🎬 تست سریال: {test_series_name}")
    print(f"🆔 Series ID: {series_id}")
    
    # بررسی وجود در فایل پردازش شده
    if series_id in scraper.processed_series:
        print("✅ سریال در فایل پردازش شده موجود است")
        series_info = scraper.processed_series[series_id]
        print(f"   نام: {series_info.get('name', 'نامشخص')}")
        print(f"   تاریخ پردازش: {series_info.get('processed_date', 'نامشخص')}")
        print(f"   تعداد قسمت: {series_info.get('episode_count', 0)}")
    else:
        print("❌ سریال در فایل پردازش شده موجود نیست")
    
    # تست با check_updates = False
    print(f"\n🔄 تست با check_updates = False:")
    scraper.check_updates = False
    
    # شبیه‌سازی process_single_series
    if series_id in scraper.processed_series:
        if hasattr(scraper, 'check_updates') and scraper.check_updates:
            print("   بررسی قسمت‌های جدید فعال است")
        else:
            print("   ⏭️ سریال قبلاً پردازش شده - باید رد شود")
            return True  # باید رد شود
    else:
        print("   🎬 سریال جدید است - باید پردازش شود")
        return False
    
    # تست با check_updates = True
    print(f"\n🔄 تست با check_updates = True:")
    scraper.check_updates = True
    
    if series_id in scraper.processed_series:
        if hasattr(scraper, 'check_updates') and scraper.check_updates:
            print("   🔍 بررسی قسمت‌های جدید فعال است - باید بررسی شود")
            return False  # باید بررسی شود
        else:
            print("   ⏭️ سریال قبلاً پردازش شده - باید رد شود")
            return True
    else:
        print("   🎬 سریال جدید است - باید پردازش شود")
        return False

if __name__ == "__main__":
    test_duplicate_check()
