#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تست تشخیص نوع لینک (دوبله یا زیرنویس)
"""

import sys
import os

# اضافه کردن مسیر فایل اصلی
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from webscraper_serial_online import OnlineSeriesScraper

def test_link_type_detection():
    """تست تشخیص نوع لینک"""
    scraper = OnlineSeriesScraper()
    
    # تست‌های مختلف
    test_cases = [
        # دوبله
        ("Foundation.S02E10.480p.WEB-DL.Farsi.Dubbed.Film2Media.mkv", True),
        ("Anne.Shirley.S01E01.1080p.WEB-DL.Farsi.Dubbed.Film2Media.mkv", True),
        ("دوبله فارسی", True),
        ("Persian Dubbed", True),
        
        # زیرنویس
        ("Foundation.S02E10.480p.WEB-DL.Farsi.Sub.Film2Media.mkv", False),
        ("Anne<PERSON>Shirley.S01E01.1080p.WEB-DL.Farsi.Sub.Film2Media.mkv", False),
        ("زیرنویس فارسی", False),
        ("Persian Subtitle", False),
        
        # نامشخص (پیش‌فرض زیرنویس)
        ("Foundation.S02E10.480p.WEB-DL.Film2Media.mkv", False),
        ("عادی", False),
    ]
    
    print("🧪 تست تشخیص نوع لینک:")
    print("=" * 50)
    
    for text, expected in test_cases:
        result = scraper.is_dubbed_version(text)
        status = "✅" if result == expected else "❌"
        link_type = "دوبله فارسی" if result else "زیرنویس فارسی"
        expected_type = "دوبله فارسی" if expected else "زیرنویس فارسی"
        
        print(f"{status} '{text}' -> {link_type} (انتظار: {expected_type})")
    
    print("=" * 50)

if __name__ == "__main__":
    test_link_type_detection()
