import requests
from bs4 import BeautifulSoup
import re

def analyze_movie_structure():
    """Analyze the exact structure of movie listings"""
    base_url = 'https://www.myf2m.net'
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    url = f"{base_url}/movies/"
    print(f"🔍 Analyzing: {url}")
    
    try:
        response = session.get(url, timeout=10)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Find posts container
        posts_container = soup.find('div', class_='posts')
        if posts_container:
            print("✅ Found posts container")
            
            # Look for articles
            articles = posts_container.find_all('article')
            print(f"📰 Found {len(articles)} articles")
            
            for i, article in enumerate(articles[:3]):
                print(f"\n--- Article {i+1} ---")
                
                # Find all links in article
                links = article.find_all('a', href=True)
                print(f"🔗 Links in article: {len(links)}")
                
                for link in links:
                    href = link.get('href')
                    title = link.get('title', '')
                    text = link.get_text().strip()
                    
                    print(f"  Link: {href}")
                    print(f"  Title: {title}")
                    print(f"  Text: {text[:50]}...")
                    print("  ---")
                
                # Check for h2 titles
                h2_title = article.find('h2', class_='entry-title')
                if h2_title:
                    title_link = h2_title.find('a')
                    if title_link:
                        print(f"🎬 Movie Title Link: {title_link.get('href')}")
                        print(f"🎬 Movie Title: {title_link.get_text().strip()}")
                
                # Check for images
                img = article.find('img')
                if img:
                    print(f"🖼️ Image src: {img.get('src')}")
                    print(f"🖼️ Image alt: {img.get('alt')}")
                
                print("-" * 50)
        
        else:
            print("❌ Posts container not found")
            
            # Try to find any articles
            all_articles = soup.find_all('article')
            print(f"📰 Total articles on page: {len(all_articles)}")
            
            if all_articles:
                print("Sample article structure:")
                article = all_articles[0]
                print(f"Classes: {article.get('class')}")
                
                # Find title
                title_elem = article.find(['h1', 'h2', 'h3'])
                if title_elem:
                    print(f"Title element: {title_elem.name}")
                    print(f"Title text: {title_elem.get_text().strip()}")
                    
                    title_link = title_elem.find('a')
                    if title_link:
                        print(f"Title link: {title_link.get('href')}")
        
        # Also check for any links that look like movie pages
        all_links = soup.find_all('a', href=True)
        movie_pattern_links = []
        
        for link in all_links:
            href = link.get('href')
            if href:
                # Look for patterns like /12345/movie-name/
                if re.search(r'/\d{4,}/[^/]+/', href):
                    movie_pattern_links.append(href)
        
        print(f"\n🎯 Links matching movie pattern: {len(movie_pattern_links)}")
        for link in movie_pattern_links[:5]:
            print(f"  - {link}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    analyze_movie_structure()
