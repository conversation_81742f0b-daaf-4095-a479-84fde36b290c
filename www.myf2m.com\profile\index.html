
<!DOCTYPE html>
<html dir="rtl" lang="fa-IR">

<!-- Mirrored from www.myf2m.com/profile/ by HTTrack Website Copier/3.x [XR&CO'2014], Fri, 18 Jul 2025 22:10:35 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->
<head>
<meta charset="UTF-8" />
<link rel="canonical" href="index.html" />
<meta name="google" content="notranslate" />
<meta name="theme-color" content="#96F207">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="msapplication-navbutton-color" content="#96F207">
<meta name="apple-mobile-web-app-status-bar-style" content="#96F207">
<meta http-equiv="X-UA-Compatible" content="IE=Edge">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<meta name="description" content="دانلود فیلم بدون سانسور , دانلود رایگان فیلم , دانلود فیلم , دانلود فیلم با زیرنویس فارسی چسبیده , پخش آنلاین فیلم و سریال , دانلود سریال بدون سانسور , دانلود فیلم جدید , فیلم جدید , فیلم 2 مدیا"/>
<meta property="og:description" content="دانلود فیلم بدون سانسور , دانلود رایگان فیلم , دانلود فیلم , دانلود فیلم با زیرنویس فارسی چسبیده , پخش آنلاین فیلم و سریال , دانلود سریال بدون سانسور , دانلود فیلم جدید , فیلم جدید , فیلم 2 مدیا"/>

<script>
            !function(e,t,n){e.yektanetAnalyticsObject=n,e[n]=e[n]||function(){e[n].q.push(arguments)},e[n].q=e[n].q||[];var a=t.getElementsByTagName("head")[0],r=new Date,c="../../cdn.yektanet.com/superscript/E46QWyD3/native-film2media.pw-2294/yn_pubec92.js?v="+r.getFullYear().toString()+"0"+r.getMonth()+"0"+r.getDate()+"0"+r.getHours(),s=t.createElement("link");s.rel="preload",s.as="script",s.href=c,a.appendChild(s);var l=t.createElement("script");l.async=!0,l.src=c,a.appendChild(l)}(window,document,"yektanet");
        </script>
    <style>
        #wpadminbar #wp-admin-bar-p404_free_top_button .ab-icon:before {
            content: "\f103";
            color: red;
            top: 2px;
        }
    </style>
<meta name='robots' content='index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1' />
	<style>img:is([sizes="auto" i], [sizes^="auto," i]) { contain-intrinsic-size: 3000px 1500px }</style>
	
	<!-- This site is optimized with the Yoast SEO plugin v24.1 - https://yoast.com/wordpress/plugins/seo/ -->
	<title>پروفایل کاربری</title>
	<meta property="og:locale" content="fa_IR" />
	<meta property="og:type" content="article" />
	<meta property="og:title" content="پروفایل کاربری" />
	<meta property="og:url" content="https://www.myf2m.com/profile/" />
	<meta property="og:site_name" content="فیلم 2 مدیا Film2Media" />
	<meta property="article:modified_time" content="2024-11-08T13:50:02+00:00" />
	<meta name="twitter:card" content="summary_large_image" />
	<script type="application/ld+json" class="yoast-schema-graph">{"@context":"https://schema.org","@graph":[{"@type":"WebPage","@id":"https://www.myf2m.com/profile/","url":"https://www.myf2m.com/profile/","name":"پروفایل کاربری","isPartOf":{"@id":"https://www.film2me.ir/#website"},"datePublished":"2024-03-19T09:34:36+00:00","dateModified":"2024-11-08T13:50:02+00:00","breadcrumb":{"@id":"https://www.myf2m.com/profile/#breadcrumb"},"inLanguage":"fa-IR","potentialAction":[{"@type":"ReadAction","target":["https://www.myf2m.com/profile/"]}]},{"@type":"BreadcrumbList","@id":"https://www.myf2m.com/profile/#breadcrumb","itemListElement":[{"@type":"ListItem","position":1,"name":"خانه","item":"https://www.film2me.ir/"},{"@type":"ListItem","position":2,"name":"پروفایل کاربری"}]},{"@type":"WebSite","@id":"https://www.film2me.ir/#website","url":"https://www.film2me.ir/","name":"فیلم 2 مدیا Film2Media","description":"دانلود فیلم و سریال بدون سانسور با زیرنویس فارسی چسبیده","potentialAction":[{"@type":"SearchAction","target":{"@type":"EntryPoint","urlTemplate":"https://www.film2me.ir/?s={search_term_string}"},"query-input":{"@type":"PropertyValueSpecification","valueRequired":true,"valueName":"search_term_string"}}],"inLanguage":"fa-IR"}]}</script>
	<!-- / Yoast SEO plugin. -->


<link data-optimized="1" rel='stylesheet' id='bootstrap-css' href='../wp-content/litespeed/css/cf398c22b633e9ab43b3616c6ff3b10d5a21.css?ver=3b10d' type='text/css' media='all' />
<link data-optimized="1" rel='stylesheet' id='stylesheet-css' href='../wp-content/litespeed/css/0ee81ed11d6aaba8294b1b4ec10b98d8e58c.css?ver=b98d8' type='text/css' media='all' />
<script type="text/javascript" src="../wp-content/themes/film2media/assets/js/vendor/jquery.min.js" id="jquery-js"></script>
<link rel='shortlink' href='../index8120.html?p=15886' />

<style>


.content-toggle {
    max-height: max-content !important;
    position: static !important;
}


#suggested-movies #cover-suggested-movies .splide__slide .cover:before{
    
    background:none !important;
    
}



@media (max-width: 991px) {

    .posts {
        display: flex !important; /* Use flexbox for horizontal layout */
        overflow-x: auto; /* Enable horizontal scrolling */
        gap: 0.35rem !important; /* Space between items */
        padding-bottom: 1rem; /* Add padding at the bottom to create space for the scrollbar */
        -webkit-overflow-scrolling: touch; /* Enable momentum scrolling on iOS */
        scrollbar-width:none;
    }

    
    .posts div {
        flex: 0 0 30%; /* Each item takes up 45% of the container */
        scroll-snap-align: start; /* Align items to the start when scrolling */
    }






    .posts .entry .entry-cover {
        height: 175px !important;
    }
    
    
    
    .posts .entry .entry-icons .icon{
        width:20px !important;
        height:20px !important;
    }
    
    .posts .entry .entry-icons .icon>svg{
        width:13px !important;
        height:13px !important;
    }
    
    
    .posts .entry .entry-ganers a{
        font-size: 7px !important;
    }
    
    .entry-meta .d-flex.align-items-end.justify-content-between.fsz-13{
        font-size:10px !important;
    }
    .entry-meta .d-flex span:first-child svg{
        width:18px !important;
        height:9px !important;
        }
    .entry-meta .d-flex span:last-child svg{
        width:9px !important;
        height:9px !important;
        }
    .entry-meta .d-flex span strong{
        font-size:10px !important;
        }
        .posts .entry .entry-title{
        font-size: 13px !important;
        margin: 0 5px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        white-space: pre-line;
        overflow:hidden;

        }
    
    .entry p{
        font-size:9px !important;
    }
    .posts .entry .entry-cover{
        margin-bottom:1rem;
    }
    
    .entry-cover{
        border-radius:.5rem !important;
    }
    .list-section:not(:last-child) {
    margin-bottom: 2.5rem !important;
}
.posts .entry .entry-cover img{
    -o-object-fit: fill !important;
    object-fit: fill !important;
}









}
   .switch-div {
  position: sticky;
  right: 20px; /* Adjust as needed */
  top: 50%; /* Center vertically */
  transform: translateY(-50%); /* Adjust for perfect centering */
  z-index: 1000; /* Ensure it stays on top */
} 


.light-mode {
    background-color: #ededed !important;
    color: #000000 !important;
    --bg-card: #ffffff;
    --bg-body: #e1e1e1;
    --text-body: #000000;
    --border-color: #999999;
    --info-lighten-color: #e4e8ed;
    --danger-lighten-color: #e9c5c5;
    --primary-color: #070c12;
    --primary-lighten-color: #727272;
    input,textarea {
    background: white !important;
}
.content-toggle:after{
    background:none !important;
}


.download-list.hardsub>.title>span {
    background-color: #c1d2e7;
}
.posts .entry .entry-ganers a{
    color:white;
}

#siteHead .menu .megasub .lists .list>li>a{
    color:#fff;
}



#menucanvas .list>li, .pagination, #siteHead .menu .megasub .lists .list>li, #siteHead .menu a, #siteHead .menu {
    color: #7e7e7e;
}

#siteHead .menu .megasub .navs .list-group.active {
    color: #ffffff;
}


#siteHead .menu .megasub .navs .list-group {
    color: white;
}

span.d-flex.flex-column.gap-05 {
    color: #fff;
}


span.d-inline-flex.align-items-center.gap-05 {
    color: white;
}


#suggested-movies #suggested-thumbnails .splide__list .splide__slide.is-active {
    border-color: rgb(233 233 233);
}


#post-intro .coverimg {
    -webkit-filter: blur(20px) brightness(4.35);
    filter: blur(20px) brightness(4.35);
}



}

.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
 
    margin-left: 10px;
}

.switch #input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #2196f3;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  z-index: 0;
  overflow: hidden;
}

.sun-moon {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: yellow;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

#input:checked + .slider {
  background-color: black;
}

#input:focus + .slider {
  box-shadow: 0 0 1px #2196f3;
}

#input:checked + .slider .sun-moon {
  -webkit-transform: translateX(26px);
  -ms-transform: translateX(26px);
  transform: translateX(26px);
  background-color: white;
  -webkit-animation: rotate-center 0.6s ease-in-out both;
  animation: rotate-center 0.6s ease-in-out both;
}

.moon-dot {
  opacity: 0;
  transition: 0.4s;
  fill: gray;
}

#input:checked + .slider .sun-moon .moon-dot {
  opacity: 1;
}

.slider.round {
  border-radius: 34px;
}

.slider.round .sun-moon {
  border-radius: 50%;
}

#moon-dot-1 {
  left: 10px;
  top: 3px;
  position: absolute;
  width: 6px;
  height: 6px;
  z-index: 4;
}

#moon-dot-2 {
  left: 2px;
  top: 10px;
  position: absolute;
  width: 10px;
  height: 10px;
  z-index: 4;
}

#moon-dot-3 {
  left: 16px;
  top: 18px;
  position: absolute;
  width: 3px;
  height: 3px;
  z-index: 4;
}

#light-ray-1 {
  left: -8px;
  top: -8px;
  position: absolute;
  width: 43px;
  height: 43px;
  z-index: -1;
  fill: white;
  opacity: 10%;
}

#light-ray-2 {
  left: -50%;
  top: -50%;
  position: absolute;
  width: 55px;
  height: 55px;
  z-index: -1;
  fill: white;
  opacity: 10%;
}

#light-ray-3 {
  left: -18px;
  top: -18px;
  position: absolute;
  width: 60px;
  height: 60px;
  z-index: -1;
  fill: white;
  opacity: 10%;
}

.cloud-light {
  position: absolute;
  fill: #eee;
  animation-name: cloud-move;
  animation-duration: 6s;
  animation-iteration-count: infinite;
}

.cloud-dark {
  position: absolute;
  fill: #ccc;
  animation-name: cloud-move;
  animation-duration: 6s;
  animation-iteration-count: infinite;
  animation-delay: 1s;
}

#cloud-1 {
  left: 30px;
  top: 15px;
  width: 40px;
}

#cloud-2 {
  left: 44px;
  top: 10px;
  width: 20px;
}

#cloud-3 {
  left: 18px;
  top: 24px;
  width: 30px;
}

#cloud-4 {
  left: 36px;
  top: 18px;
  width: 40px;
}

#cloud-5 {
  left: 48px;
  top: 14px;
  width: 20px;
}

#cloud-6 {
  left: 22px;
  top: 26px;
  width: 30px;
}

@keyframes cloud-move {
  0% {
    transform: translateX(0px);
  }

  40% {
    transform: translateX(4px);
  }

  80% {
    transform: translateX(-4px);
  }

  100% {
    transform: translateX(0px);
  }
}

.stars {
  transform: translateY(-32px);
  opacity: 0;
  transition: 0.4s;
}

.star {
  fill: white;
  position: absolute;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  animation-name: star-twinkle;
  animation-duration: 2s;
  animation-iteration-count: infinite;
}

#input:checked + .slider .stars {
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
  opacity: 1;
}

#star-1 {
  width: 20px;
  top: 2px;
  left: 3px;
  animation-delay: 0.3s;
}

#star-2 {
  width: 6px;
  top: 16px;
  left: 3px;
}

#star-3 {
  width: 12px;
  top: 20px;
  left: 10px;
  animation-delay: 0.6s;
}

#star-4 {
  width: 18px;
  top: 0px;
  left: 18px;
  animation-delay: 1.3s;
}

@keyframes star-twinkle {
  0% {
    transform: scale(1);
  }

  40% {
    transform: scale(1.2);
  }

  80% {
    transform: scale(0.8);
  }

  100% {
    transform: scale(1);
  }
}
.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.switch #input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #2196f3;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  z-index: 0;
  overflow: hidden;
}

.sun-moon {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: yellow;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

#input:checked + .slider {
  background-color: black;
}

#input:focus + .slider {
  box-shadow: 0 0 1px #2196f3;
}

#input:checked + .slider .sun-moon {
  -webkit-transform: translateX(26px);
  -ms-transform: translateX(26px);
  transform: translateX(26px);
  background-color: white;
  -webkit-animation: rotate-center 0.6s ease-in-out both;
  animation: rotate-center 0.6s ease-in-out both;
}

.moon-dot {
  opacity: 0;
  transition: 0.4s;
  fill: gray;
}

#input:checked + .slider .sun-moon .moon-dot {
  opacity: 1;
}

.slider.round {
  border-radius: 34px;
}

.slider.round .sun-moon {
  border-radius: 50%;
}

#moon-dot-1 {
  left: 10px;
  top: 3px;
  position: absolute;
  width: 6px;
  height: 6px;
  z-index: 4;
}

#moon-dot-2 {
  left: 2px;
  top: 10px;
  position: absolute;
  width: 10px;
  height: 10px;
  z-index: 4;
}

#moon-dot-3 {
  left: 16px;
  top: 18px;
  position: absolute;
  width: 3px;
  height: 3px;
  z-index: 4;
}

#light-ray-1 {
  left: -8px;
  top: -8px;
  position: absolute;
  width: 43px;
  height: 43px;
  z-index: -1;
  fill: white;
  opacity: 10%;
}

#light-ray-2 {
  left: -50%;
  top: -50%;
  position: absolute;
  width: 55px;
  height: 55px;
  z-index: -1;
  fill: white;
  opacity: 10%;
}

#light-ray-3 {
  left: -18px;
  top: -18px;
  position: absolute;
  width: 60px;
  height: 60px;
  z-index: -1;
  fill: white;
  opacity: 10%;
}

.cloud-light {
  position: absolute;
  fill: #eee;
  animation-name: cloud-move;
  animation-duration: 6s;
  animation-iteration-count: infinite;
}

.cloud-dark {
  position: absolute;
  fill: #ccc;
  animation-name: cloud-move;
  animation-duration: 6s;
  animation-iteration-count: infinite;
  animation-delay: 1s;
}

#cloud-1 {
  left: 30px;
  top: 15px;
  width: 40px;
}

#cloud-2 {
  left: 44px;
  top: 10px;
  width: 20px;
}

#cloud-3 {
  left: 18px;
  top: 24px;
  width: 30px;
}

#cloud-4 {
  left: 36px;
  top: 18px;
  width: 40px;
}

#cloud-5 {
  left: 48px;
  top: 14px;
  width: 20px;
}

#cloud-6 {
  left: 22px;
  top: 26px;
  width: 30px;
}

@keyframes cloud-move {
  0% {
    transform: translateX(0px);
  }

  40% {
    transform: translateX(4px);
  }

  80% {
    transform: translateX(-4px);
  }

  100% {
    transform: translateX(0px);
  }
}

.stars {
  transform: translateY(-32px);
  opacity: 0;
  transition: 0.4s;
}

.star {
  fill: white;
  position: absolute;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  animation-name: star-twinkle;
  animation-duration: 2s;
  animation-iteration-count: infinite;
}

#input:checked + .slider .stars {
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
  opacity: 1;
}

#star-1 {
  width: 20px;
  top: 2px;
  left: 3px;
  animation-delay: 0.3s;
}

#star-2 {
  width: 6px;
  top: 16px;
  left: 3px;
}

#star-3 {
  width: 12px;
  top: 20px;
  left: 10px;
  animation-delay: 0.6s;
}

#star-4 {
  width: 18px;
  top: 0px;
  left: 18px;
  animation-delay: 1.3s;
}

@keyframes star-twinkle {
  0% {
    transform: scale(1);
  }

  40% {
    transform: scale(1.2);
  }

  80% {
    transform: scale(0.8);
  }

  100% {
    transform: scale(1);
  }
}





#post-intro .entry-poster .btn-tracking {
    top: 1rem;
    left: 4rem;
    position: absolute;
    width: 42px;
    height: 42px;
    border-radius: 50px;
}


        /* Quick access menu styles */
        .quick-access-menu {
            position: fixed;
            bottom: 8%;
            left: 0;
            right: 0;
            background-color: var(--bg-body);
            box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .quick-access-menu ul {
            display: flex;
            justify-content: space-around;
            padding: 10px 0;
            margin: 0;
            list-style: none;
        }

        .quick-access-menu li {
            position: relative;
        }

        .quick-access-menu a {
            text-decoration: none;
            color: var(--text-body);
            padding: 10px 15px;
            transition: color 0.3s;
        }

        .quick-access-menu a.active {
            color: red; /* Change color for active link */
        }

        .quick-access-menu a::after {
            content: '';
            display: block;
            height: 2px;
            background: red;
            position: absolute;
            left: 50%;
            right: 50%;
            bottom: -5px; /* Adjust as needed */
            transition: left 0.3s, right 0.3s;
        }

        .quick-access-menu a.active::after {
            left: 0;
            right: 0;
        }



@media (max-width: 768px) { 
    .float-search.bg-card.border.fsz-13 {
width: 700%;
        left: -125%;
    }
}


.download-season .series-downloaditems a button {
    padding: 0 .8rem !important;

}
.download-season .series-downloaditems a:first-child {
    margin-left: 3%;

}
.download-season .series-downloaditems .btn{
    place-content: center;

}




.main-footer-insta {
    background: linear-gradient(145deg, #FA9F6F, #FA697C);
    box-shadow: 0 3px 20px rgba(250, 123, 119, .3);
}
.main-footer-telegram {
    background: linear-gradient(145deg, #96D5F5, #5EBAE8);
    box-shadow: 0 3px 20px rgba(142, 209, 243, .35);
}
.ficon-instagram,.ficon-telegram{
    
        margin-left: 10px;

}


.ficon-instagram,.ficon-telegram{

    width: 22px;
    height: 22px;
    line-height: 1;
    font-weight: 300;
    transform: translate(-5px, 0);
    display: inline-block;
    fill:#fff;


}
.ficon-instagram g,.ficon-telegram g{
    fill:#fff;

}


.main-footer-insta .ficon-arrow-left-sign, .main-footer-telegram .ficon-arrow-left-sign {
    width: 22px;
    height: 22px;
    display: inline-block;
    left: 15px;
    top: 15px;
    position: absolute;
    border-radius: 100px;
    font-size: 10px;
    background: rgba(255, 255, 255, .15);
    fill:#fff;
}

.position-absolute {
    position: absolute !important;
}


    </style>






</head>

<body class="rtl">


<!--[if lte IE 8]><p class="alert alert-danger rounded-0 position-fixed bottom-0" role="alert">‫ <strong>مرورگر قدیمی!</strong> این وب سایت قابلیت های محدودی را در این مرورگر ارائه خواهد کرد. ما فقط نسخه های جدید مرورگرهای بزرگ مانند کروم، فایرفاکس، سافاری، و Edge را پیشنهاد میکنیم.</p><![endif]-->
<svg xmlns="http://www.w3.org/2000/svg" id="iconsax" class="d-none">
	<symbol id="icon-imdb" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 575 289.8" fill="black">
	<path fill="#f6c700" d="M575 25c-1.6-12.8-11-23-23-25H23.2A28.6 28.6 0 0 0 0 28.6V261c0 16 12.4 29 27.6 29h520c14 0 25.7-11 27.4-25.3V24.9Z"/>
	<path d="M69.3 58.2H115V234H69.3V58.2Z"/>
	<path d="M201.2 139.2c-4-26.8-6.1-41.7-6.5-44.7-2-14.3-3.8-26.8-5.5-37.4H130v175.6h40l.2-116 16.8 116h28.4l16-118.5.1 118.5h39.9V57.1h-59.6l-10.6 82Z"/>
	<path d="M346.7 93.6c.5 2.3.8 7.4.8 15.3v68c0 11.8-.8 19-2.3 21.6-1.5 2.6-5.6 4-12.1 4V87c5 0 8.3.6 10.1 1.6 1.9 1 3 2.7 3.5 5ZM367.3 231c5.4-1.1 10-3.2 13.7-6.2 3.7-3 6.3-7.2 7.8-12.5s2.3-15.8 2.3-31.6V119c0-16.7-.6-27.8-1.6-33.5-1-5.6-3.6-10.8-7.6-15.4-4-4.6-10-8-17.8-10-7.8-2-20.5-3-42.6-3h-34v175.6h55.3c12.7-.4 20.9-1 24.5-1.8Z"/>
	<path d="M464.8 204.7c-.9 2.2-4.6 3.4-7.3 3.4-2.8 0-4.6-1.1-5.5-3.3a48 48 0 0 1-1.4-14.8v-46.4c0-8 .4-13 1.3-15 .8-2 2.5-3 5.2-3 2.8 0 6.5 1.2 7.5 3.4 1 2.3 1.4 7.2 1.4 14.6v45c-.3 9.2-.7 14.6-1.2 16.1Zm-58.1 26.5h41l2.9-11.2c3.7 4.5 7.8 7.9 12.3 10.1a40 40 0 0 0 16.3 3.4 31 31 0 0 0 18.7-5.7c5.3-3.8 8.6-8.2 10-13.4 1.4-5.1 2.2-13 2.2-23.5v-49.3c0-10.6-.3-17.5-.8-20.8A23.7 23.7 0 0 0 495 103a40.1 40.1 0 0 0-15.7-2.8c-5.3 0-12 1-16.5 3-4.5 2.1-8.5 5.3-12.2 9.5V55.6h-44v175.6Z"/>
	</symbol>

	<symbol id="icon-metacritic" viewBox="0 0 40 40">
		<path fill="black" d="M36.98 19.49a17.49 17.49 0 1 1 0-.02"/>
		<path fill="#F2F2F2" d="m17.2 32.94 3.42-3.41-6.57-6.57a3.28 3.28 0 0 1-.74-1.01c-.36-.79-.53-2 .37-2.9 1.11-1.11 2.58-.65 4.01.78l6.32 6.31 3.4-3.41-6.58-6.59c-.28-.28-.6-.7-.76-1.04-.44-.9-.42-2.02.39-2.83 1.13-1.13 2.6-.72 4.24.92l6.13 6.13 3.4-3.41-6.63-6.64c-3.36-3.36-6.52-3.25-8.69-1.08a5.74 5.74 0 0 0-1.59 2.7 6.71 6.71 0 0 0-.09 2.8l-.04.05c-1.66-.69-3.55-.27-5 1.18a5.52 5.52 0 0 0-1.64 5.18l-.07.07-1.68-1.36-2.95 2.95c1.04.95 2.28 2.1 3.69 3.5l7.67 7.68Z"/>
		<path fill="#FFBD3F" d="M19.98 0A20 20 0 1 0 40 20v-.02A20 20 0 0 0 19.98 0Zm-.09 4.27a15.66 15.66 0 0 1 15.68 15.65v.02A15.66 15.66 0 1 1 19.89 4.27Z"/>
	</symbol>
	
	
	
	

	<symbol id="icon-rotten-tomatoes" viewBox="0 0 143.75 142.5">
		<path d="m36.984 2.8681-8.605 7.0948 11.704 10.114c-14.776-5.554-27.219 7.873-28.176 13.146 7.782-1.816 12.59-2.372 18.801-1.882-39.592 26.053-27.984 73.919-8.065 90.479 32.519 25.93 77.417 18 100.69-7.4 33.93-36.423 9.94-107.9-58.269-96.004 0.597-6.577 3.558-8.4485 6.989-9.0035-5.004-8.3923-20.631-4.129-25.618 7.7215-0.151 0.358-9.448-14.266-9.448-14.266z" fill="#f93208"/>
		<path d="m122.25 126.31v4.6562h1.375v-4.6562l1.5-0.008v-1.3125l-4.4141-0.0195 0.00005 1.332z" fill="#f93208"/>
		<path d="m127.48 125.02-1.2813 0.0156v5.9688h1.3594v-3.25l1.7656 2.4688v-2.4062zm3.6992 0.008-1.8555 2.793-0.008 2.4062 1.7852-2.4648v3.25h1.3594v-5.9688z" fill="#f93208"/>
		<g fill="#fff" transform="matrix(.33241 0 0 .33241 106.85 43.6)">
			<path d="m-58.803 89.458-22.078 0.2659h-23.141v-24.742-24.742l69.978 0.32551c38.487 0.17903 71.638-0.14101 71.638-0.14101l0.46355 49.256-23.943-0.39885h-22.48v63.696 63.962h-24.685l-25.748-0.2659c-0.000293-44.258-0.0036-81.844-0.0036-127.22z"/>
			<path d="m-220.7 175.11-13.892-0.1806v-87.709-87.709h41.908c45.203 0 49.02 0.21732 60.054 3.419 17.906 5.1956 31.575 16.455 39.108 32.213 3.6631 7.6624 5.0989 13.79 5.4749 23.367 0.84948 21.642-8.3313 40.459-24.584 50.391-4.8682 2.9749-5.0947 3.2102-4.3022 4.4697 2.1387 3.3989 35.93 61.595 35.93 61.881 0 0.17761-12.896 0.32291-28.657 0.32291h-28.657l-16.634-28.013c-9.1486-15.407-16.986-28.495-17.416-29.082-0.58267-0.79684-2.0664-1.1577-5.8174-1.4148l-5.0353-0.34512 0.31448 29.427 0.31449 29.427-12.108-0.14346c-6.6594-0.0788-18.36-0.22479-26-0.32411zm61.863-97.862c11.726-2.467 17.722-8.0859 18.379-17.22 0.45852-6.387-1.0591-10.745-5.1178-14.697-5.3791-5.2375-12.887-7.0524-29.328-7.0889l-8.6782-0.01924 0.40567 5.715c0.22313 3.1432 0.40566 12.317 0.40566 20.387v14.672l9.8334-0.42512c5.4084-0.23383 11.754-0.82913 14.101-1.3229z"/>
		</g>
	</symbol>
	
	
	
	
	
	<symbol id="icon-home" fill="currentColor" viewBox="0 0 24 24"><path opacity=".4" d="m20.04 6.822-5.76-4.03c-1.57-1.1-3.98-1.04-5.49.13l-5.01 3.91c-1 .78-1.79 2.38-1.79 3.64v6.9c0 2.55 2.07 4.63 4.62 4.63h10.78c2.55 0 4.62-2.07 4.62-4.62v-6.78c0-1.35-.87-3.01-1.97-3.78Z"/><path d="M12 18.75c-.41 0-.75-.34-.75-.75v-3c0-.41.34-.75.75-.75s.75.34.75.75v3c0 .41-.34.75-.75.75Z"/></symbol>
	<symbol id="icon-persons" fill="currentColor" viewBox="0 0 24 24"><path opacity=".4" d="M9 2C6.38 2 4.25 4.13 4.25 6.75c0 2.57 2.01 4.65 4.63 4.74.08-.01.16-.01.22 0h.07a4.738 4.738 0 0 0 4.58-4.74C13.75 4.13 11.62 2 9 2Z"/><path d="M14.08 14.149c-2.79-1.86-7.34-1.86-10.15 0-1.27.85-1.97 2-1.97 3.23s.7 2.37 1.96 3.21c1.4.94 3.24 1.41 5.08 1.41 1.84 0 3.68-.47 5.08-1.41 1.26-.85 1.96-1.99 1.96-3.23-.01-1.23-.7-2.37-1.96-3.21Z"/><path opacity=".4" d="M19.99 7.338c.16 1.94-1.22 3.64-3.13 3.87h-.05c-.06 0-.12 0-.17.02-.97.05-1.86-.26-2.53-.83 1.03-.92 1.62-2.3 1.5-3.8a4.64 4.64 0 0 0-.77-2.18 3.592 3.592 0 0 1 5.15 2.92Z"/><path d="M21.988 16.59c-.08.97-.7 1.81-1.74 2.38-1 .55-2.26.81-3.51.78.72-.65 1.14-1.46 1.22-2.32.1-1.24-.49-2.43-1.67-3.38-.67-.53-1.45-.95-2.3-1.26 2.21-.64 4.99-.21 6.7 1.17.92.74 1.39 1.67 1.3 2.63Z"/></symbol>
	<symbol id="icon-categories" fill="currentColor" viewBox="0 0 24 24"><path d="M7.24 2h-1.9C3.15 2 2 3.15 2 5.33v1.9c0 2.18 1.15 3.33 3.33 3.33h1.9c2.18 0 3.33-1.15 3.33-3.33v-1.9C10.57 3.15 9.42 2 7.24 2Z"/><path opacity=".4" d="M18.67 2h-1.9c-2.18 0-3.33 1.15-3.33 3.33v1.9c0 2.18 1.15 3.33 3.33 3.33h1.9c2.18 0 3.33-1.15 3.33-3.33v-1.9C22 3.15 20.85 2 18.67 2Z"/><path d="M18.67 13.43h-1.9c-2.18 0-3.33 1.15-3.33 3.33v1.9c0 2.18 1.15 3.33 3.33 3.33h1.9c2.18 0 3.33-1.15 3.33-3.33v-1.9c0-2.18-1.15-3.33-3.33-3.33Z"/><path opacity=".4" d="M7.24 13.43h-1.9C3.15 13.43 2 14.58 2 16.76v1.9C2 20.85 3.15 22 5.33 22h1.9c2.18 0 3.33-1.15 3.33-3.33v-1.9c.01-2.19-1.14-3.34-3.32-3.34Z"/></symbol>
	<symbol id="icon-vip" viewBox="0 0 24 24" fill="currentColor"><path opacity=".4" d="M16.77 18.98H7.23c-.42 0-.8-.27-.94-.66L2.13 6.67c-.33-.93.73-1.72 1.52-1.15l4 2.86c.53.38 1.29.15 1.52-.46l1.89-5.04c.32-.87 1.55-.87 1.87 0l1.89 5.04a1 1 0 0 0 1.52.46l4-2.86c.8-.57 1.85.23 1.52 1.15L17.7 18.32c-.13.39-.51.66-.93.66Z"/><path d="M17 22H7c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h10c.41 0 .75.34.75.75s-.34.75-.75.75ZM14.5 14.75h-5c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h5c.41 0 .75.34.75.75s-.34.75-.75.75Z"/></symbol>
	<symbol id="icon-play" viewBox="0 0 24 24" fill="currentColor"><path d="M18.7 8.98 4.14 17.71c-.09-.33-.14-.68-.14-1.04V7.33c0-3.08 3.33-5 6-3.46l4.04 2.33 4.05 2.34c.***********.61.44Z"/><path opacity=".4" d="m18.089 15.46-4.05 2.34-4.04 2.33c-1.91 1.1-4.16.44-5.28-1.17l.42-.25 14.44-8.66c1 1.8.51 4.26-1.49 5.41Z"/></symbol>

	<symbol id="icon-user" fill="currentColor" viewBox="0 0 30 30"><path d="M15 2.5a6 6 0 0 0-.2 11.9h.4A6 6 0 0 0 15 2.5Z" opacity=".4"/><path d="M21.4 17.7a12.4 12.4 0 0 0-12.7 0c-1.6 1-2.5 2.5-2.5 4 0 1.6.9 3 2.5 4 1.7 1.2 4 1.8 6.3 1.8s4.6-.6 6.4-1.8c1.5-1 2.4-2.5 2.4-4s-.9-3-2.4-4Z"/></symbol>
	<symbol id="icon-search" viewBox="0 0 24 24" fill="currentColor"><path d="M11.5 21.75c-5.65 0-10.25-4.6-10.25-10.25S5.85 1.25 11.5 1.25s10.25 4.6 10.25 10.25-4.6 10.25-10.25 10.25Zm0-19c-4.83 0-8.75 3.93-8.75 8.75s3.92 8.75 8.75 8.75 8.75-3.93 8.75-8.75-3.92-8.75-8.75-8.75ZM22 22.751c-.19 0-.38-.07-.53-.22l-2-2a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2 2c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22Z"/></symbol>
	<symbol id="icon-bell" viewBox="0 0 24 24" fill="currentColor"><path d="M12.02 20.532c-2.33 0-4.66-.37-6.87-1.11-.84-.29-1.48-.88-1.76-1.65-.29-.77-.19-1.62.27-2.38l1.15-1.91c.24-.4.46-1.2.46-1.67v-2.89c0-3.72 3.03-6.75 6.75-6.75s6.75 3.03 6.75 6.75v2.89c0 .46.22 1.27.46 1.68l1.14 1.9c.43.72.51 1.59.22 2.38a2.72 2.72 0 0 1-1.71 1.65c-2.2.74-4.53 1.11-6.86 1.11Zm0-16.86c-2.89 0-5.25 2.35-5.25 5.25v2.89c0 .73-.3 1.81-.67 2.44l-1.15 1.91c-.22.37-.28.76-.15 1.09.12.34.42.6.83.74a20 20 0 0 0 12.79 0c.36-.12.64-.39.77-.75s.1-.75-.1-1.08l-1.15-1.91c-.38-.65-.67-1.72-.67-2.45v-2.88c0-2.9-2.35-5.25-5.25-5.25Z"/><path d="M13.88 3.94c-.07 0-.14-.01-.21-.03-.29-.08-.57-.14-.84-.18-.85-.11-1.67-.05-2.44.18-.28.09-.58 0-.77-.21a.742.742 0 0 1-.14-.78 2.724 2.724 0 0 1 2.55-1.74c1.14 0 2.14.68 2.55 1.74.1.27.05.57-.14.78-.15.16-.36.24-.56.24ZM12.02 22.809c-.99 0-1.95-.4-2.65-1.1-.7-.7-1.1-1.66-1.1-2.65h1.5c0 .59.24 1.17.66 1.59.42.42 1 .66 1.59.66 1.24 0 2.25-1.01 2.25-2.25h1.5c0 2.07-1.68 3.75-3.75 3.75Z"/></symbol>
	
	<symbol id="icon-calendar" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24"><path d="M2 12c0-3.771 0-5.657 1.172-6.828S6.229 4 10 4h4c3.771 0 5.657 0 6.828 1.172S22 8.229 22 12v2c0 3.771 0 5.657-1.172 6.828S17.771 22 14 22h-4c-3.771 0-5.657 0-6.828-1.172S2 17.771 2 14z"/><path stroke-linecap="round" d="M7 4V2.5M17 4V2.5M2.5 9h19"/></symbol>
	<symbol id="icon-dubbled" viewBox="0 0 24 24" fill="currentColor"><path d="M12 19.75c-3.72 0-6.75-3.03-6.75-6.75V8c0-3.72 3.03-6.75 6.75-6.75S18.75 4.28 18.75 8v5c0 3.72-3.03 6.75-6.75 6.75Zm0-17C9.11 2.75 6.75 5.1 6.75 8v5c0 2.9 2.36 5.25 5.25 5.25s5.25-2.35 5.25-5.25V8c0-2.9-2.36-5.25-5.25-5.25Z"/><path d="M12 22.75c-5.38 0-9.75-4.37-9.75-9.75v-2c0-.41.34-.75.75-.75s.75.34.75.75v2c0 4.55 3.7 8.25 8.25 8.25s8.25-3.7 8.25-8.25v-2c0-.41.34-.75.75-.75s.75.34.75.75v2c0 5.38-4.37 9.75-9.75 9.75ZM14.612 8.229c-.08 0-.17-.01-.26-.04a7.373 7.373 0 0 0-4.99 0 .742.742 0 0 1-.95-.45.75.75 0 0 1 .45-.96c1.94-.7 4.07-.7 6.01 0 .39.14.59.57.45.96-.11.31-.4.49-.71.49Z"/><path d="M13.702 11.23c-.06 0-.13-.01-.2-.03-1.07-.29-2.2-.29-3.27 0a.75.75 0 1 1-.39-1.45c1.33-.36 2.73-.36 4.06 0a.754.754 0 0 1-.2 1.48Z"/></symbol>
	<symbol id="icon-subtitled" viewBox="0 0 20 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M17.083 0.5H2.91634C1.76759 0.5 0.833008 1.43458 0.833008 2.58333V13.4167C0.833008 14.5654 1.76759 15.5 2.91634 15.5H17.083C18.2318 15.5 19.1663 14.5654 19.1663 13.4167V2.58333C19.1663 1.43458 18.2318 0.5 17.083 0.5ZM8.95801 5.91667C8.95801 6.14667 8.77134 6.33333 8.54134 6.33333H6.24967V9.66667H8.54134C8.77134 9.66667 8.95801 9.85333 8.95801 10.0833V11.3333C8.95801 11.5633 8.77134 11.75 8.54134 11.75H5.83301C4.91259 11.75 4.16634 11.0037 4.16634 10.0833V5.91667C4.16634 4.99625 4.91259 4.25 5.83301 4.25H8.54134C8.77134 4.25 8.95801 4.43667 8.95801 4.66667V5.91667ZM15.833 5.91667C15.833 6.14667 15.6463 6.33333 15.4163 6.33333H13.1247V9.66667H15.4163C15.6463 9.66667 15.833 9.85333 15.833 10.0833V11.3333C15.833 11.5633 15.6463 11.75 15.4163 11.75H12.708C11.7876 11.75 11.0413 11.0037 11.0413 10.0833V5.91667C11.0413 4.99625 11.7876 4.25 12.708 4.25H15.4163C15.6463 4.25 15.833 4.43667 15.833 4.66667V5.91667Z"/></symbol>

	<symbol id="icon-dash-dashboard" viewBox="0 0 24 24" fill="currentColor"><path d="M11 19.9V4.1C11 2.6 10.36 2 8.77 2H4.73C3.14 2 2.5 2.6 2.5 4.1v15.8c0 1.5.64 2.1 2.23 2.1h4.04c1.59 0 2.23-.6 2.23-2.1Z"/><path opacity=".4" d="M21.5 10.9V4.1c0-1.5-.64-2.1-2.23-2.1h-4.04C13.64 2 13 2.6 13 4.1v6.8c0 1.5.64 2.1 2.23 2.1h4.04c1.59 0 2.23-.6 2.23-2.1ZM21.5 19.9v-2.8c0-1.5-.64-2.1-2.23-2.1h-4.04c-1.59 0-2.23.6-2.23 2.1v2.8c0 1.5.64 2.1 2.23 2.1h4.04c1.59 0 2.23-.6 2.23-2.1Z"/></symbol>
	<symbol id="icon-dash-watchlist" viewBox="0 0 24 24" fill="currentColor"><path opacity=".4" d="M22 7.81v8.38c0 3.64-2.17 5.81-5.81 5.81H7.81C4.17 22 2 19.83 2 16.19V7.81c0-.51.04-1 .13-1.45C2.64 3.61 4.67 2.01 7.77 2h8.46c3.1.01 5.13 1.61 5.64 **********.13.94.13 1.45Z"/><path d="M22 7.81v.05H2v-.05c0-.51.04-1 .13-1.45h5.64V2h1.5v4.36h5.46V2h1.5v4.36h5.64c.***********.13 1.45ZM14.441 12.72l-2.08-1.2c-.77-.44-1.51-.5-2.09-.17-.58.33-.9 1.01-.9 1.89v2.4c0 .88.32 1.56.9 **********.***********.4 0 .83-.13 1.27-.38l2.08-1.2c.77-.44 1.19-1.06 1.19-1.73 0-.67-.43-1.26-1.19-1.71Z"/></symbol>
	<symbol id="icon-dash-tracking" viewBox="0 0 24 24" fill="currentColor"><path opacity=".4" d="M22 7.81v8.38c0 3.64-2.17 5.81-5.81 5.81H7.81C4.17 22 2 19.83 2 16.19V7.81c0-.51.04-1 .13-1.45C2.64 3.61 4.67 2.01 7.77 2h8.46c3.1.01 5.13 1.61 5.64 **********.13.94.13 1.45Z"/><path d="M22 7.81v.05H2v-.05c0-.51.04-1 .13-1.45h5.64V2h1.5v4.36h5.46V2h1.5v4.36h5.64c.***********.13 1.45ZM14.441 12.72l-2.08-1.2c-.77-.44-1.51-.5-2.09-.17-.58.33-.9 1.01-.9 1.89v2.4c0 .88.32 1.56.9 **********.***********.4 0 .83-.13 1.27-.38l2.08-1.2c.77-.44 1.19-1.06 1.19-1.73 0-.67-.43-1.26-1.19-1.71Z"/></symbol>
	<symbol id="icon-dash-subscription" viewBox="0 0 24 24" fill="currentColor"><use xlink:href="#icon-vip" /></symbol>
	<symbol id="icon-dash-tickets" viewBox="0 0 24 24" fill="currentColor"><path opacity=".4" d="M22 6.25v5.1c0 1.27-.42 2.34-1.17 3.08-.74.75-1.81 1.17-3.08 1.17v1.81c0 .68-.76 1.09-1.32.71l-.97-.64c.09-.31.13-.65.13-1.01V12.4c0-2.04-1.36-3.4-3.4-3.4H5.4c-.14 0-.27.01-.4.02V6.25C5 3.7 6.7 2 9.25 2h8.5C20.3 2 22 3.7 22 6.25Z"/><path d="M15.59 12.4v4.07c0 .36-.04.7-.13 1.01-.37 1.47-1.59 2.39-3.27 2.39H9.47l-3.02 2.01a.671.671 0 0 1-1.05-.56v-1.45c-1.02 0-1.87-.34-2.46-.93-.6-.6-.94-1.45-.94-2.47V12.4c0-1.9 1.18-3.21 3-3.38.13-.01.26-.02.4-.02h6.79c2.04 0 3.4 1.36 3.4 3.4Z"/></symbol>
	<symbol id="icon-dash-logout" viewBox="0 0 24 24" fill="currentColor"><path d="M9 7.2v9.59C9 20 11 22 14.2 22h2.59c3.2 0 5.2-2 5.2-5.2V7.2C22 4 20 2 16.8 2h-2.6C11 2 9 4 9 7.2z" opacity=".4"/><path d="M5.57 8.12l-3.35 3.35c-.29.29-.29.77 0 1.06l3.35 3.35c.*********** 1.06 0 .29-.29.29-.77 0-1.06l-2.07-2.07h10.69c.41 0 .75-.34.75-.75s-.34-.75-.75-.75H4.56l2.07-2.07c.15-.15.22-.34.22-.53s-.07-.39-.22-.53c-.29-.3-.76-.3-1.06 0z"/></symbol>
</svg>
<div id="auth-wrap">
	<div class="container-fluid">
		<form id="authform" action="#" class="bg-body rounded p-3 p-lg-5 fsz-14" method="post">
			<p class="text-center mb-5"><a href="../index.html" class="custom-logo-link" rel="home"><svg xmlns="http://www.w3.org/2000/svg" width="149" height="29" fill="currentColor" viewBox="0 0 149 29"><path class="text-body" d="M106 6.8h-4.4a4.2 4.2 0 0 0-4.1 4.2v1.2c0 2.4 1.9 4.4 4.3 4.4h3.3c.7 0 1.3-.6 1.3-1.4 0-.7-.6-1.4-1.3-1.4h-3.3c-1 0-1.7-.7-1.7-1.6V11c0-.9.7-1.5 1.5-1.5h4.5c1.2 0 2.2 1 2.2 2.3v6.9c0 1-.7 1.7-1.7 1.7h-5.1c-.6 0-1.2-.2-1.8-.7-.6-.3-1.4-1.3-2.2-2.2-1-1.1-2-2.3-3.3-3.5-.9-.8-2.1-1-3.2-.5a3 3 0 0 0-1.8 2.8V20c0 .2 0 .2-.2.3h-.3L83 14.5l-.4-.3V8.1c0-.7-.7-1.3-1.4-1.3s-1.3.6-1.3 1.3v12l-.2.3h-.4l-6-4.9v-4.4c0-1.7-1-3.2-2.5-4a4.2 4.2 0 0 0-4.6.8l-12 13c-.5.5-.4 1.4.1 1.9a1.3 1.3 0 0 0 1.9-.1L68 9.8c.5-.3 1.1-.4 1.7-.2.5.3.9.8.9 1.5V20c0 .2-.2.3-.3.3 0 .1-.2.1-.4 0l-4.7-3.4c-.6-.5-1.5-.3-1.9.3-.4.6-.3 1.4.3 1.9l4.7 3.4a3 3 0 0 0 3.2.3 3 3 0 0 0 1.7-2.8v-1l4.4 3.5a3 3 0 0 0 3.3.3 3 3 0 0 0 1.7-2.8v-2l4.2 4.3a3 3 0 0 0 3.3.6A3 3 0 0 0 92 20v-3.8c0-.2 0-.2.2-.3h.3l3 3.3 2.7 2.7c1.1.7 2.2 1.1 3.4 1.1h5.1c2.4 0 4.4-2 4.4-4.4v-7a5 5 0 0 0-5-5ZM101.3 5.8c.8 0 1.4-.6 1.4-1.3V1.4c0-.8-.6-1.4-1.4-1.4-.7 0-1.3.6-1.3 1.4v3c0 .8.6 1.4 1.3 1.4ZM91.3 25h-5.6c-.7 0-1.3.6-1.3 1.3 0 .8.6 1.4 1.3 1.4h5.6c.8 0 1.4-.6 1.4-1.4 0-.7-.6-1.3-1.4-1.3ZM35.4 9.6c-1.5-.6-3-.2-4.1 1l-4 4.5c-.5.6-.4 1.5.2 2 .5.5 1.4.4 1.8-.2l4-4.5a1 1 0 0 1 1.1-.3c.2 0 .7.3.7 1v5.7c0 .9-.7 1.6-1.6 1.6H28c-.4 0-.8-.2-1-.5l-3.2-3.6v-5.5c0-.8-.6-1.4-1.3-1.4-.7 0-1.3.6-1.3 1.4v8.7c0 .5-.4.9-.9.9h-3.6c-.8 0-1.4.6-1.4 1.4 0 .7.6 1.3 1.4 1.3h3.6c1.7 0 3.1-1.2 3.5-2.8l1.1 1.4a4.2 4.2 0 0 0 3.2 1.4h5.4c2.4 0 4.3-2 4.3-4.3V13c0-1.6-1-3-2.4-3.5ZM10 25H4.4c-.7 0-1.3.6-1.3 1.3 0 .8.6 1.4 1.3 1.4H10c.8 0 1.4-.6 1.4-1.4 0-.7-.6-1.3-1.4-1.3ZM13.1 9.4c-.7 0-1.3.6-1.3 1.4v8.7c0 .5-.4.9-.9.9H3.5a.8.8 0 0 1-.8-.8V8.1c0-.7-.6-1.3-1.4-1.3C.6 6.8 0 7.4 0 8v11.5c0 2 1.6 3.5 3.5 3.5h7.4c2 0 3.6-1.6 3.6-3.6v-8.7c0-.8-.6-1.4-1.4-1.4Z"/><path class="text-body" d="M137.1 29h-8.3c-3 0-5.6-1.9-6.5-4.8-1-3 0-6 2.5-7.8l8.2-6h-9.8c-.8 0-1.4-.6-1.4-1.5 0-.8.6-1.4 1.4-1.4h14.2c.7 0 1.2.4 1.4 1 .2.6 0 1.3-.5 1.6l-11.8 8.7a4 4 0 0 0-1.5 4.5 3.9 3.9 0 0 0 3.8 2.8h8.3c.8 0 1.4.6 1.4 1.4 0 .8-.6 1.5-1.4 1.5Z"/><path class="text-primary" d="M51.4 23.1H41c-.6 0-1-.4-1.3-1-.1-.5 0-1.1.5-1.4l8.7-6.4c1.1-.8 1.2-2 1-3-.3-.8-1.1-1.8-2.5-1.8h-6.1c-.8 0-1.4-.6-1.4-1.3 0-.8.6-1.4 1.4-1.4h6c2.4 0 4.4 1.5 5.1 3.7.7 2.3 0 4.6-2 6l-5.2 3.9h6.2c.8 0 1.4.6 1.4 1.4 0 .7-.6 1.3-1.4 1.3ZM147.6 22.4h-14.3c-.6 0-1.1-.4-1.3-1-.2-.6 0-1.3.5-1.6l11.8-8.7a4 4 0 0 0 1.5-4.5 3.9 3.9 0 0 0-3.8-2.8h-8.3c-.8 0-1.5-.6-1.5-1.4 0-.9.7-1.5 1.5-1.5h8.3c3 0 5.6 1.9 6.5 4.8 1 3 0 6-2.5 7.8l-8.2 6h9.8c.8 0 1.4.6 1.4 1.4 0 .9-.6 1.5-1.4 1.5Z"/></svg></a></p>

							<div class="action d-flex align-items-center bg-card mb-4">
					<a href="index.html" class="btn btn-block fw-700 btn-primary">ورود</a>
					<a href="index0ddc.html?action=register" class="btn btn-block fw-700">عضویت</a>
				</div>
			
							<p class="form-floating mb-3">
					<input type="text" id="input-username" class="form-control" name="username" placeholder="نام کاربری" value="" required>
					<label class="form-label" for="input-username">نام کاربری</label>
				</p>
				<p class="form-floating mb-3">
					<input type="password" id="input-password" class="form-control" name="password" placeholder="رمز عبور" value="" required>
					<label class="form-label" for="input-password">رمز عبور</label>
				</p>
			
			<div class="row g-3 mb-3">
				<div class="col-7 col-md">
					<p class="form-floating m-0">
						<input type="tel" class="form-control" id="input-challenge" name="challenge_captcha" placeholder="کد امنیتی" pattern="[0-9]{5}" maxlength="5" required autocomplete="off" dir="rtl" required>
						<label class="form-label" for="input-challenge">کد امنیتی</label>
					</p>
				</div>
				<div class="col-5 col-md-auto">
				    <input type="hidden" name="native"  />
					<button type="button" class="btn p-0 overflow-hidden h-100"><img src="https://www.myf2m.net/captcha.php?w=150&amp;h=50" alt="کپچا" width="150" height="50" style="width:100%;height:100%"></button>
				</div>
			</div>

			<button type="submit" name="submit" id="authbutton" value="d65d6818b7" class="btn btn-block btn-primary fw-700">ورود به حساب</button>

							<p class="text-center text-muted mt-4"><a href="index53d5.html?action=forgot">رمز عبورم را فراموش کرده‌ام</a></p>
					</form>
		<p class="text-center mt-4 fsz-14"><a href="../index.html">بازگشت به صفحه اصلی</a></p>
	</div>
	</div>


<script type="text/javascript" src="../wp-content/themes/film2media/assets/js/vendor/bootstrap.js" id="bootstrap-js"></script>
<script id="themejs" src="../wp-content/themes/film2media/assets/js/script.min.js" data-ajax="https://www.myf2m.com/wp-admin/admin-ajax.php" defer></script><!-- Google tag (gtag.js) -->
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-27MEFBC6W4');
  
  
  
  
  
  
  const toggle = document.getElementById('input');
const body = document.body;

if (localStorage.getItem('theme') === 'light') {
    body.classList.add('light-mode');
    toggle.checked = false;
}

toggle.addEventListener('change', () => {
    if (toggle.checked) {
        
        
        body.classList.remove('light-mode');
        localStorage.setItem('theme', 'dark'); 
        
        
    } else {
        
        
        
        body.classList.add('light-mode');
        localStorage.setItem('theme', 'light'); 
    }
});
</script>
<script>
    // Function to handle the click event on quick access menu links
    document.querySelectorAll('.quick-access-menu a').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault(); // Prevent default anchor click behavior

            // Remove active class from all links
            document.querySelectorAll('.quick-access-menu a').forEach(item => {
                item.classList.remove('active');
            });

            // Add active class to the clicked link
            this.classList.add('active');

            // Scroll to the corresponding section
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            targetElement.scrollIntoView({ behavior: 'smooth' });
        });
    });

    // Set up the Intersection Observer
    const sections = document.querySelectorAll('section');
    const options = {
        root: null, // Use the viewport as the root
        threshold: 0.5 // Trigger when 50% of the section is visible
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            const id = entry.target.getAttribute('id');
            const menuLink = document.querySelector(`.quick-access-menu a[href="#${id}"]`);

            if (entry.isIntersecting) {
                // Add active class to the corresponding menu link
                document.querySelectorAll('.quick-access-menu a').forEach(item => {
                    item.classList.remove('active');
                });
                menuLink.classList.add('active');
            }
        });
    }, options);

    // Observe each section
    sections.forEach(section => {
        observer.observe(section);
    });
    
    
    
    
    
    
    
    		$(document).on('click', '[data-tracking]', function(e){
			e.preventDefault();
			const $BTN = $(this);
			$BTN.html('<span class="spinner-border" style="border-width:2px" role="status"><span class="sr-only">Loading...</span></span>');
			$.post(ajaxURL, {
				action: 'user_tracking',
				post_id: $BTN.data('tracking'),
			},
			function( data ) {
				if( data.response == 'added' ){
					$BTN.attr('title','حذف از لیست پیگیری').html('<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><use xlink:href="#icon-minus" /></svg>');
				} else {
					$BTN.attr('title','افزودن به لیست پیگیری').html('<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><use xlink:href="#icon-bell" /></svg>');
				}
				
			});
		});
		
		
		
</script>


</body>

<!-- Mirrored from www.myf2m.com/profile/ by HTTrack Website Copier/3.x [XR&CO'2014], Fri, 18 Jul 2025 22:10:35 GMT -->
</html>


<!-- Page cached by LiteSpeed Cache 6.5.4 on 2025-07-19 01:25:08 -->