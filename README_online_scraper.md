# اسکریپت اسکرپ آنلاین سریال‌ها از myf2m.com

## توضیحات
این اسکریپت برای اسکرپ آنلاین اطلاعات سریال‌ها از سایت myf2m.com طراحی شده است. اسکریپت قابلیت استخراج اطلاعات کامل سریال‌ها شامل نام، امتیاز، پوستر، قسمت‌ها و لینک‌های دانلود را دارد.

## ویژگی‌ها
- ✅ اسکرپ آنلاین از 32 صفحه سریال
- ✅ استخراج اطلاعات کامل سریال‌ها
- ✅ استخراج قسمت‌ها و لینک‌های دانلود
- ✅ ذخیره در فایل‌های CSV جداگانه
- ✅ مدیریت خطا و تاخیر هوشمند
- ✅ قابلیت ادامه از جایی که متوقف شده
- ✅ گزارش‌گیری کامل

## نصب وابستگی‌ها
```bash
pip install -r requirements_online.txt
```

## نحوه استفاده
```bash
python webscraper_serial_online.py
```

## فایل‌های خروجی
1. **series_info_online.csv** - اطلاعات پایه سریال‌ها
2. **series_episodes_online.csv** - قسمت‌ها و لینک‌های دانلود
3. **series_details_online.csv** - جزئیات کامل سریال‌ها
4. **processed_series_online.json** - لیست سریال‌های پردازش شده
5. **scraped_urls.json** - لیست URL های اسکرپ شده
6. **series_scraper_online.log** - فایل لاگ

## تنظیمات
- صفحه شروع و پایان قابل تنظیم
- تاخیر بین درخواست‌ها: 1-3 ثانیه
- تاخیر بین صفحات: 2 ثانیه
- تعداد تلاش مجدد: 3 بار

## نکات مهم
- اسکریپت به صورت خودکار تاخیر اعمال می‌کند
- در صورت قطع شدن، از جایی که متوقف شده ادامه می‌دهد
- تمام خطاها در فایل لاگ ثبت می‌شوند
- فایل‌های CSV با encoding UTF-8-BOM ذخیره می‌شوند

## مثال خروجی CSV

### series_info_online.csv
```csv
series_id,persian_name,english_name,release_date,rating,poster_url,url,date_added
abc123,بریکینگ بد,Breaking Bad,2008,9.5,https://...,https://...,2025-07-23 10:30:00
```

### series_episodes_online.csv
```csv
series_id,season_number,episode_number,episode_title,download_link,quality,file_size,episode_date,link_type
abc123,1,1,قسمت اول,https://...,1080p,1.2GB,2025-07-23,مستقیم
```

## پشتیبانی
در صورت بروز مشکل، فایل لاگ را بررسی کنید یا با توسعه‌دهنده تماس بگیرید.
