# اسکریپت استخراج داده‌های سریال‌ها از myf2m.net

## توضیحات

این اسکریپت برای استخراج اطلاعات کامل سریال‌ها از فایل‌های HTML دانلود شده از سایت myf2m.net طراحی شده است. اسکریپت قابلیت استخراج اطلاعات پایه سریال‌ها، جزئیات فصل‌ها و قسمت‌ها، و لینک‌های دانلود با کیفیت‌های مختلف را دارد.

## ویژگی‌ها

### 🎬 استخراج اطلاعات پایه سریال‌ها
- نام فارسی و انگلیسی سریال
- سال انتشار
- امتیاز IMDB
- URL پوستر
- مسیر فایل HTML

### 📺 استخراج اطلاعات فصل‌ها و قسمت‌ها
- شماره فصل و نام فصل
- تعداد قسمت‌ها در هر فصل
- لینک‌های دانلود با کیفیت‌های مختلف (1080p, 720p, 480p)
- تشخیص نوع (دوبله فارسی یا زیرنویس چسبیده)

### 🎭 استخراج جزئیات سریال
- خلاصه داستان
- ژانرهای سریال
- اطلاعات کارگردان و بازیگران
- کشور سازنده و زبان
- مدت زمان هر قسمت
- وضعیت سریال (در حال پخش یا تمام شده)

### 🔄 قابلیت‌های پیشرفته
- به‌روزرسانی تدریجی (اضافه کردن قسمت‌های جدید بدون تکرار)
- سیستم لاگ‌گیری کامل
- آمارگیری دقیق از فرآیند اسکرپ
- ذخیره پیشرفت کار

## فایل‌های خروجی

### 1. `series_info.csv`
اطلاعات پایه سریال‌ها:
```csv
series_id,persian_name,english_name,release_date,rating,poster_url,url,date_added
```

### 2. `series_episodes.csv`
اطلاعات قسمت‌ها و لینک‌های دانلود:
```csv
series_id,season_number,episode_number,episode_title,download_link,quality,file_size,episode_date,link_type
```

### 3. `series_details.csv`
جزئیات کامل سریال‌ها:
```csv
series_id,plot_summary,genres,total_seasons,total_episodes,series_status,cast_info,director,country,language,duration
```

## نصب و راه‌اندازی

### پیش‌نیازها
```bash
pip install beautifulsoup4 lxml
```

### ساختار پوشه‌ها
```
project/
├── webscraper_serial.py
├── myf2m_series/
│   └── www.myf2m.net/
│       └── series/
│           ├── peaky-blinders/
│           │   └── index.html
│           ├── dexter/
│           │   └── index.html
│           └── ...
└── output files (CSV)
```

## نحوه استفاده

### 1. اسکرپ کامل
```bash
python webscraper_serial.py --mode full
```

### 2. به‌روزرسانی تدریجی
```bash
python webscraper_serial.py --mode incremental
```

### 3. تست روی تعداد محدود
```bash
python webscraper_serial.py --mode test --test-count 5
```

### 4. تعیین پوشه سریال‌ها
```bash
python webscraper_serial.py --folder path/to/series/folder
```

## پارامترهای خط فرمان

- `--mode`: حالت اجرا (`full`, `incremental`, `test`)
- `--folder`: مسیر پوشه حاوی فایل‌های سریال (پیش‌فرض: `myf2m_series`)
- `--test-count`: تعداد سریال‌ها برای تست (پیش‌فرض: 5)

## مثال خروجی

### نمونه اطلاعات استخراج شده:

**series_info.csv:**
```csv
9e00cfe2c7c0,پیکی بلایندرز,Peaky Blinders,2013,8.7,https://example.com/poster.jpg,/path/to/file.html,2025-07-23T04:36:52
```

**series_episodes.csv:**
```csv
9e00cfe2c7c0,1,1,قسمت 01,https://download.link/episode1.mkv,BluRay 1080p,,,dubbed
9e00cfe2c7c0,1,2,قسمت 02,https://download.link/episode2.mkv,BluRay 1080p,,,dubbed
```

**series_details.csv:**
```csv
9e00cfe2c7c0,"داستان خانواده‌ای از گنگسترها در بیرمنگام","جنایی, درام, تاریخی",6,36,completed,"Cillian Murphy, Paul Anderson","Steven Knight","United Kingdom","English",60 دقیقه
```

## لاگ‌ها و آمار

اسکریپت اطلاعات کاملی از فرآیند اسکرپ ارائه می‌دهد:

```
2025-07-23 04:36:51,780 - INFO - 🧪 شروع تست روی 2 سریال
2025-07-23 04:36:51,796 - INFO - 🎬 64 سریال یافت شد
2025-07-23 04:36:52,005 - INFO - ✅ پیکی بلایندرز پردازش شد

==================================================
📊 آمار نهایی اسکرپ سریال‌ها
==================================================
🎬 تعداد کل سریال‌ها: 64
✅ سریال‌های پردازش شده: 64
🆕 قسمت‌های جدید: 0
❌ خطاها: 0
⏱️ مدت زمان: 0:02:15.492318
==================================================
```

## فایل‌های پشتیبان

- `processed_series.json`: لیست سریال‌های پردازش شده برای به‌روزرسانی تدریجی
- `series_scraper.log`: فایل لاگ کامل

## نکات مهم

1. **کیفیت داده‌ها**: اسکریپت تلاش می‌کند تا حداکثر اطلاعات را استخراج کند، اما کیفیت خروجی به کیفیت HTML ورودی بستگی دارد.

2. **به‌روزرسانی تدریجی**: برای سریال‌هایی که قسمت‌های جدید اضافه شده، از حالت `incremental` استفاده کنید.

3. **مدیریت حافظه**: برای تعداد زیاد سریال‌ها، اسکریپت هر 10 سریال پیشرفت را ذخیره می‌کند.

4. **خطاها**: تمام خطاها در فایل لاگ ثبت می‌شوند و اسکریپت ادامه کار می‌دهد.

## عیب‌یابی

### مشکلات رایج:

1. **پوشه سریال‌ها یافت نشد**:
   ```
   پوشه سریال‌ها یافت نشد: myf2m_series/www.myf2m.net/series
   ```
   **حل**: مطمئن شوید مسیر پوشه صحیح است.

2. **خطا در پارس HTML**:
   ```
   خطا در استخراج نام سریال: ...
   ```
   **حل**: فایل HTML ممکن است خراب باشد.

3. **مشکل در کدگذاری**:
   ```
   UnicodeDecodeError: ...
   ```
   **حل**: مطمئن شوید فایل‌های HTML با کدگذاری UTF-8 ذخیره شده‌اند.

## مجوز

این اسکریپت برای استفاده شخصی و آموزشی ارائه شده است. لطفاً قوانین کپی‌رایت را رعایت کنید.

## تماس و پشتیبانی

برای گزارش باگ یا درخواست ویژگی جدید، لطفاً یک issue ایجاد کنید.