#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from webscraper_serial_online import OnlineSeriesScraper

def test_without_omdb():
    """تست برنامه بدون OMDb"""
    scraper = OnlineSeriesScraper()
    
    print("🔍 تست برنامه بدون OMDb")
    print("="*50)
    print(f"OMDb فعال: {getattr(scraper, 'omdb_enabled', True)}")
    
    # تست استخراج اطلاعات یک سریال
    url = 'https://www.myf2m.net/series/the-bear-series/'
    print(f"\n🎬 تست سریال: {url}")
    
    # استخراج قسمت‌ها
    episodes = scraper.extract_episodes_and_links(url)
    
    if episodes:
        print(f"✅ {len(episodes)} قسمت استخراج شد")
        
        # نمایش نمونه
        first_episode = episodes[0]
        print(f"\n📺 نمونه قسمت:")
        print(f"   فصل: {first_episode['season_number']}")
        print(f"   قسمت: {first_episode['episode_number']}")
        print(f"   عنوان: {first_episode['episode_title']}")
        print(f"   تعداد لینک: {len(first_episode['download_links'])}")
        
        if first_episode['download_links']:
            first_link = first_episode['download_links'][0]
            print(f"   نوع صدا: {first_link.get('link_type', 'نامشخص')}")
            print(f"   کیفیت: {first_link.get('quality', 'نامشخص')}")
    else:
        print("❌ هیچ قسمتی استخراج نشد")

if __name__ == "__main__":
    test_without_omdb()
