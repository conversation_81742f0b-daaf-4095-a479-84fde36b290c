#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from webscraper_serial_online import OnlineSeriesScraper
from bs4 import BeautifulSoup
import re

def analyze_series_structure():
    scraper = OnlineSeriesScraper()

    # سریال‌های مختلف برای تحلیل (URL های واقعی)
    test_series = [
        ('Nine Perfect Strangers', 'https://www.myf2m.net/series/nine-perfect-strangers/'),
        ('The Bear', 'https://www.myf2m.net/series/the-bear-series/'),
        ('Dexter Resurrection', 'https://www.myf2m.net/series/dexter-resurrection/')
    ]
    
    for series_name, url in test_series:
        print(f"\n{'='*60}")
        print(f"🎬 تحلیل ساختار: {series_name}")
        print(f"🔗 URL: {url}")
        print('='*60)
        
        response = scraper.make_request(url)
        if not response:
            print("❌ خطا در دریافت صفحه")
            continue
            
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 1. تحلیل لینک‌های مستقیم ویدیو
        print("\n📺 1. لینک‌های مستقیم ویدیو:")
        video_links = soup.find_all('a', href=lambda x: x and any(ext in x for ext in ['.mkv', '.mp4', '.avi']))
        print(f"   تعداد: {len(video_links)}")
        
        for i, link in enumerate(video_links[:3]):
            href = link.get('href', '')
            text = link.get_text().strip()
            print(f"   {i+1}. {text[:50]}... -> {href[:60]}...")
        
        # 2. تحلیل باکس‌های دانلود
        print("\n📦 2. باکس‌های دانلود:")
        download_boxes = soup.find_all(['div', 'section'], class_=lambda x: x and ('download' in x.lower() or 'episode' in x.lower()))
        print(f"   تعداد: {len(download_boxes)}")
        
        # 3. تحلیل متن‌های حاوی قسمت
        print("\n📝 3. متن‌های حاوی قسمت:")
        episode_texts = soup.find_all(string=lambda text: text and ('قسمت' in text or 'Episode' in text))
        print(f"   تعداد: {len(episode_texts)}")
        
        for i, text in enumerate(episode_texts[:5]):
            clean_text = ' '.join(text.strip().split())
            print(f"   {i+1}. {clean_text[:60]}...")
        
        # 4. تحلیل عناصر حاوی فصل و قسمت
        print("\n🎯 4. عناصر حاوی فصل و قسمت:")
        season_episode_elements = soup.find_all(['div', 'span', 'p', 'h1', 'h2', 'h3'], 
                                              string=lambda text: text and 
                                              ('فصل' in text and 'قسمت' in text))
        print(f"   تعداد: {len(season_episode_elements)}")
        
        for i, elem in enumerate(season_episode_elements[:3]):
            text = elem.get_text().strip()
            parent_class = elem.parent.get('class', []) if elem.parent else []
            print(f"   {i+1}. {text[:50]}... (parent class: {parent_class})")
        
        # 5. تحلیل لینک‌های دوبله و زیرنویس
        print("\n🎵 5. تحلیل دوبله و زیرنویس:")
        
        # جستجوی کلمات کلیدی
        dubbed_elements = soup.find_all(string=lambda text: text and 'دوبله' in text)
        subtitle_elements = soup.find_all(string=lambda text: text and 'زیرنویس' in text)
        
        print(f"   دوبله: {len(dubbed_elements)} مورد")
        print(f"   زیرنویس: {len(subtitle_elements)} مورد")
        
        # نمونه‌های دوبله
        for i, text in enumerate(dubbed_elements[:2]):
            clean_text = ' '.join(text.strip().split())
            print(f"     دوبله {i+1}: {clean_text[:50]}...")
        
        # نمونه‌های زیرنویس
        for i, text in enumerate(subtitle_elements[:2]):
            clean_text = ' '.join(text.strip().split())
            print(f"     زیرنویس {i+1}: {clean_text[:50]}...")
        
        # 6. تحلیل ساختار DOM
        print("\n🏗️ 6. ساختار DOM:")
        
        # جستجوی div های اصلی
        main_divs = soup.find_all('div', class_=True)
        content_divs = [div for div in main_divs if 
                       any(keyword in ' '.join(div.get('class', [])).lower() 
                           for keyword in ['content', 'episode', 'download', 'season'])]
        
        print(f"   div های محتوا: {len(content_divs)}")
        for i, div in enumerate(content_divs[:3]):
            classes = ' '.join(div.get('class', []))
            print(f"     {i+1}. class: {classes}")
        
        print("\n" + "-"*60)

if __name__ == "__main__":
    analyze_series_structure()
