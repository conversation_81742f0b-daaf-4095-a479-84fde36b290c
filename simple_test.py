#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🎬 شروع تست ساده...")

try:
    from webscraper_serial_online import OnlineSeriesScraper
    print("✅ ماژول بارگذاری شد")
    
    scraper = OnlineSeriesScraper()
    print("✅ اسکریپر ایجاد شد")
    
    # تست ساده
    test_url = 'https://www.myf2m.com/series/the-bear-season-4/'
    print(f"🔍 تست URL: {test_url}")
    
    episodes = scraper.extract_episodes_and_links(test_url)
    print(f"📺 تعداد قسمت‌ها: {len(episodes) if episodes else 0}")
    
    if episodes:
        # نمایش اولین قسمت
        first_episode = episodes[0]
        print(f"🎯 اولین قسمت: فصل {first_episode['season_number']}, قسمت {first_episode['episode_number']}")
        
        # نمایش لینک‌های دانلود
        for i, link in enumerate(first_episode['download_links'][:2]):
            link_type = link.get('link_type', 'نامشخص')
            print(f"   لینک {i+1}: {link['quality']} - {link_type}")
    
    print("✅ تست کامل شد")
    
except Exception as e:
    print(f"❌ خطا: {e}")
    import traceback
    traceback.print_exc()
