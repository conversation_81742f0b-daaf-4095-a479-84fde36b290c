#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from webscraper_serial_online import OnlineSeriesScraper

def test_episodes():
    scraper = OnlineSeriesScraper()

    # تست استخراج قسمت‌ها از سریال چند فصلی
    test_url = 'https://www.myf2m.com/series/the-bear-season-4/'

    print('🎬 تست استخراج قسمت‌ها از The Bear...')
    print('=' * 60)

    episodes = scraper.extract_episodes_and_links(test_url)

    if episodes:
        print(f'✅ کل قسمت‌های یافت شده: {len(episodes)}')

        # گروه‌بندی بر اساس فصل
        seasons = {}
        for episode in episodes:
            season = episode['season_number']
            if season not in seasons:
                seasons[season] = []
            seasons[season].append(episode)

        print(f'📊 تعداد فصل‌ها: {len(seasons)}')

        # بررسی قسمت‌هایی که ممکن است نسخه‌های مختلف داشته باشند
        print('\n🔍 بررسی قسمت‌هایی که ممکن است نسخه‌های مختلف داشته باشند:')

        # شمارش قسمت‌ها بر اساس شماره
        episode_counts = {}
        for episode in episodes:
            key = f"S{episode['season_number']}E{episode['episode_number']}"
            if key not in episode_counts:
                episode_counts[key] = []
            episode_counts[key].append(episode)

        # نمایش قسمت‌هایی که بیش از یک نسخه دارند
        multi_version_episodes = {k: v for k, v in episode_counts.items() if len(v) > 1}

        if multi_version_episodes:
            print(f'📊 {len(multi_version_episodes)} قسمت با نسخه‌های مختلف یافت شد:')
            for key, versions in list(multi_version_episodes.items())[:5]:
                print(f'   {key}: {len(versions)} نسخه')
                for i, version in enumerate(versions):
                    links = version["download_links"]
                    for link in links:
                        link_type = link.get('link_type', 'نامشخص')
                        print(f'     نسخه {i+1}: {link["quality"]} - {link_type}')
        else:
            print('❌ هیچ قسمتی با نسخه‌های مختلف یافت نشد')

        print('\n📺 خلاصه فصل‌ها:')
        for season_num in sorted(seasons.keys()):
            season_episodes = seasons[season_num]
            print(f'   فصل {season_num}: {len(season_episodes)} قسمت')

            # نمایش چند قسمت اول
            for i, episode in enumerate(season_episodes[:2]):
                links = episode["download_links"]
                print(f'     - قسمت {episode["episode_number"]}: {len(links)} لینک')

            if len(season_episodes) > 2:
                print(f'     ... و {len(season_episodes) - 2} قسمت دیگر')
    else:
        print('❌ هیچ قسمتی یافت نشد')

if __name__ == "__main__":
    test_episodes()
