#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from webscraper_serial_online import OnlineSeriesScraper

def test_id_generation():
    """تست تولید ID سریال‌ها"""
    scraper = OnlineSeriesScraper()
    
    print("🔍 تست تولید ID سریال‌ها")
    print("="*50)
    
    # تست چند سریال
    test_series = [
        "دانلود سریال The Bear فصل 4 بدون سانسور با زیرنویس فارسی چسبیده",
        "دانلود سریال نه غریبه کامل Nine Perfect Strangers بدون سانسور با زیرنویس فارسی چسبیده",
        "دانلود انیمه شیطان کش Demon Slayer Kimetsu no Yaiba بدون سانسور با زیرنویس فارسی چسبیده"
    ]
    
    test_urls = [
        "https://www.myf2m.com/series/the-bear-series/",
        "https://www.myf2m.com/series/nine-perfect-strangers/",
        "https://www.myf2m.com/series/demon-slayer-kimetsu-no-yaiba/"
    ]
    
    for i, (name, url) in enumerate(zip(test_series, test_urls)):
        print(f"\n🎬 سریال {i+1}: {name[:50]}...")
        
        # تولید ID چندین بار
        id1 = scraper.generate_series_id(name, url)
        id2 = scraper.generate_series_id(name, url)
        id3 = scraper.generate_series_id(name, url)
        
        print(f"   ID 1: {id1}")
        print(f"   ID 2: {id2}")
        print(f"   ID 3: {id3}")
        
        if id1 == id2 == id3:
            print("   ✅ ID ثابت است")
        else:
            print("   ❌ ID متغیر است!")
        
        # بررسی وجود در فایل پردازش شده
        if id1 in scraper.processed_series:
            print(f"   ✅ در فایل پردازش شده موجود است")
            series_info = scraper.processed_series[id1]
            print(f"      نام ذخیره شده: {series_info.get('name', 'نامشخص')[:50]}...")
        else:
            print(f"   ❌ در فایل پردازش شده موجود نیست")

if __name__ == "__main__":
    test_id_generation()
