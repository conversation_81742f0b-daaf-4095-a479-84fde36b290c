#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests

def test_tmdb_api():
    """تست TMDB API"""
    
    # کلید API واقعی TMDB (رایگان)
    api_key = '8265bd1679663a7ea12ac168da84d2e8'
    base_url = 'https://api.themoviedb.org/3'
    
    print("🔍 تست TMDB API")
    print("="*50)
    
    # تست 1: جستجوی سریال
    print("1️⃣ تست جستجوی سریال...")
    search_url = f"{base_url}/search/tv"
    params = {
        'api_key': api_key,
        'query': 'The Bear',
        'language': 'en-US'
    }
    
    try:
        response = requests.get(search_url, params=params, timeout=10)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('results'):
                print(f"   ✅ {len(data['results'])} نتیجه یافت شد")
                first_result = data['results'][0]
                print(f"   نام: {first_result.get('name', 'نامشخص')}")
                print(f"   TMDB ID: {first_result.get('id', 'نامشخص')}")
                print(f"   تاریخ پخش: {first_result.get('first_air_date', 'نامشخص')}")
                print(f"   امتیاز: {first_result.get('vote_average', 'نامشخص')}")
                
                # تست 2: دریافت جزئیات
                tmdb_id = first_result.get('id')
                if tmdb_id:
                    print(f"\n2️⃣ تست دریافت جزئیات...")
                    detail_url = f"{base_url}/tv/{tmdb_id}"
                    detail_params = {
                        'api_key': api_key,
                        'language': 'en-US'
                    }
                    
                    detail_response = requests.get(detail_url, params=detail_params, timeout=10)
                    if detail_response.status_code == 200:
                        detail_data = detail_response.json()
                        print(f"   ✅ جزئیات دریافت شد")
                        print(f"   تعداد فصل‌ها: {detail_data.get('number_of_seasons', 'نامشخص')}")
                        print(f"   تعداد قسمت‌ها: {detail_data.get('number_of_episodes', 'نامشخص')}")
                        print(f"   وضعیت: {detail_data.get('status', 'نامشخص')}")
                        
                        genres = [genre['name'] for genre in detail_data.get('genres', [])]
                        print(f"   ژانرها: {', '.join(genres) if genres else 'نامشخص'}")
                    else:
                        print(f"   ❌ خطا در دریافت جزئیات: {detail_response.status_code}")
            else:
                print("   ❌ نتیجه‌ای یافت نشد")
        else:
            print(f"   ❌ خطا در جستجو: {response.status_code}")
            if response.status_code == 401:
                print("   کلید API نامعتبر است")
            
    except Exception as e:
        print(f"   ❌ خطا در اتصال: {e}")

if __name__ == "__main__":
    test_tmdb_api()
