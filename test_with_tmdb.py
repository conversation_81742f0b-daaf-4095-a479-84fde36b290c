#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from webscraper_serial_online import OnlineSeriesScraper

def test_with_tmdb():
    """تست برنامه با TMDB API"""
    scraper = OnlineSeriesScraper()
    
    print("🔍 تست برنامه با TMDB API")
    print("="*50)
    print(f"TMDB فعال: {getattr(scraper, 'tmdb_enabled', False)}")
    print(f"OMDb فعال: {getattr(scraper, 'omdb_enabled', False)}")
    
    # تست استخراج جزئیات یک سریال
    url = 'https://www.myf2m.net/series/the-bear-series/'
    print(f"\n🎬 تست سریال: {url}")
    
    # استخراج جزئیات کامل
    details = scraper.extract_series_details(url)
    
    if details:
        print(f"✅ جزئیات استخراج شد")
        print(f"\n📊 اطلاعات کلی:")
        print(f"   نام فارسی: {details.get('persian_name', 'نامشخص')}")
        print(f"   نام انگلیسی: {details.get('english_name', 'نامشخص')}")
        print(f"   سال: {details.get('year', 'نامشخص')}")
        print(f"   امتیاز: {details.get('imdb_rating', 'نامشخص')}")
        print(f"   تعداد فصل‌ها: {details.get('total_seasons', 'نامشخص')}")
        print(f"   تعداد قسمت‌ها: {details.get('total_episodes', 'نامشخص')}")
        
        genres = details.get('genres', 'نامشخص')
        print(f"   ژانرها: {genres}")
        
        plot = details.get('plot_summary', '')
        if plot:
            print(f"   خلاصه: {plot[:100]}...")
        
        print(f"\n📺 اطلاعات قسمت‌ها:")
        episodes = details.get('episodes', [])
        if episodes:
            print(f"   تعداد قسمت‌های استخراج شده: {len(episodes)}")
            
            # آمار فصل‌ها
            seasons = {}
            for episode in episodes:
                season = episode.get('season_number', 1)
                if season not in seasons:
                    seasons[season] = 0
                seasons[season] += 1
            
            print(f"   فصل‌های موجود:")
            for season in sorted(seasons.keys()):
                print(f"     فصل {season}: {seasons[season]} قسمت")
        else:
            print("   ❌ قسمتی استخراج نشد")
    else:
        print("❌ جزئیات استخراج نشد")

if __name__ == "__main__":
    test_with_tmdb()
