#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from webscraper_serial_online import OnlineSeriesScraper
from bs4 import BeautifulSoup
import re

def debug_genres():
    """دیباگ استخراج ژانرها"""
    scraper = OnlineSeriesScraper()
    
    url = 'https://www.myf2m.net/series/the-bear-series/'
    print(f"🔍 دیباگ ژانرها برای: {url}")
    
    response = scraper.make_request(url)
    if not response:
        print("❌ خطا در دریافت صفحه")
        return
    
    soup = BeautifulSoup(response.content, 'html.parser')
    
    print("\n1️⃣ جستجوی بخش ژانرها...")
    genre_section = soup.find(string=lambda text: text and 'ژانرها' in text)
    if genre_section:
        print(f"   ✅ بخش ژانرها یافت شد: {genre_section.strip()}")
        
        # پیدا کردن والد این متن
        parent = genre_section.parent
        if parent:
            print(f"   والد: {parent.name}")
            
            # جستجوی لینک‌ها در والد
            genre_links = parent.find_all('a')
            print(f"   تعداد لینک‌ها: {len(genre_links)}")
            
            genres = []
            for link in genre_links:
                genre_name = link.get_text(strip=True)
                print(f"     لینک: '{genre_name}'")
                if genre_name and genre_name not in genres:
                    genres.append(genre_name)
            
            print(f"   ژانرهای استخراج شده: {genres}")
    else:
        print("   ❌ بخش ژانرها یافت نشد")
    
    print("\n2️⃣ جستجوی با regex...")
    genre_pattern = r'ژانرها\s*:\s*([^قسمت\n]+)'
    genre_match = re.search(genre_pattern, response.text)
    if genre_match:
        genre_text = genre_match.group(1).strip()
        print(f"   ✅ متن ژانرها: '{genre_text[:100]}...'")
        
        # تقسیم بر اساس لینک‌ها
        genre_soup = BeautifulSoup(genre_text, 'html.parser')
        genre_links = genre_soup.find_all('a')
        print(f"   تعداد لینک‌ها: {len(genre_links)}")
        
        genres = []
        for link in genre_links:
            genre_name = link.get_text(strip=True)
            print(f"     لینک: '{genre_name}'")
            if genre_name and genre_name not in genres:
                genres.append(genre_name)
        
        print(f"   ژانرهای استخراج شده: {genres}")
        print(f"   نوع genres: {type(genres)}")
        
        # تست join
        genres_str = ', '.join(genres) if genres else 'نامشخص'
        print(f"   genres_str: '{genres_str}'")
    else:
        print("   ❌ الگوی regex یافت نشد")

    print("\n3️⃣ جستجوی مستقیم لینک‌های ژانر...")
    genre_links = soup.find_all('a', href=lambda x: x and '/genres/' in x)
    print(f"   تعداد لینک‌های ژانر: {len(genre_links)}")

    genres = []
    for link in genre_links:
        genre_name = link.get_text(strip=True)
        print(f"     لینک: '{genre_name}' - href: {link.get('href', '')}")
        if genre_name and len(genre_name) > 1 and genre_name not in genres:
            genres.append(genre_name)

    print(f"   ژانرهای نهایی: {genres}")
    genres_str = ', '.join(genres) if genres else 'نامشخص'
    print(f"   genres_str نهایی: '{genres_str}'")

if __name__ == "__main__":
    debug_genres()
