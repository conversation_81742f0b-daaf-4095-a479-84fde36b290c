#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import pandas as pd
from webscraper_serial_online import OnlineSeriesScraper

def migrate_series_ids():
    """مهاجرت ID های سریال‌ها به نسخه جدید"""
    scraper = OnlineSeriesScraper()
    
    print("🔄 مهاجرت ID های سریال‌ها")
    print("="*50)
    
    # بارگذاری فایل‌های قدیمی
    try:
        with open('processed_series_online.json', 'r', encoding='utf-8') as f:
            old_processed = json.load(f)
        print(f"📂 {len(old_processed)} سریال قدیمی بارگذاری شد")
    except:
        print("❌ فایل processed_series_online.json یافت نشد")
        return
    
    try:
        df_info = pd.read_csv('series_info_online.csv')
        print(f"📂 {len(df_info)} سریال در فایل info")
    except:
        print("❌ فایل series_info_online.csv یافت نشد")
        return
    
    try:
        df_episodes = pd.read_csv('series_episodes_online.csv')
        print(f"📂 {len(df_episodes)} قسمت در فایل episodes")
    except:
        print("❌ فایل series_episodes_online.csv یافت نشد")
        return
    
    # ایجاد mapping از ID قدیمی به جدید
    id_mapping = {}
    new_processed = {}
    
    for old_id, series_data in old_processed.items():
        series_name = series_data.get('name', '')
        series_url = series_data.get('url', '')
        
        if series_name and series_url:
            # تولید ID جدید
            new_id = scraper.generate_series_id(series_name, series_url)
            id_mapping[old_id] = new_id
            
            # کپی داده‌ها با ID جدید
            new_processed[new_id] = series_data.copy()
            
            print(f"🔄 {old_id} → {new_id} ({series_name[:30]}...)")
    
    print(f"\n📊 {len(id_mapping)} ID مهاجرت یافت")
    
    # بروزرسانی فایل processed_series_online.json
    try:
        with open('processed_series_online.json', 'w', encoding='utf-8') as f:
            json.dump(new_processed, f, ensure_ascii=False, indent=2)
        print("✅ فایل processed_series_online.json بروزرسانی شد")
    except Exception as e:
        print(f"❌ خطا در بروزرسانی processed_series: {e}")
        return
    
    # بروزرسانی فایل series_info_online.csv
    try:
        df_info['series_id'] = df_info['series_id'].map(id_mapping).fillna(df_info['series_id'])
        df_info.to_csv('series_info_online.csv', index=False, encoding='utf-8')
        print("✅ فایل series_info_online.csv بروزرسانی شد")
    except Exception as e:
        print(f"❌ خطا در بروزرسانی series_info: {e}")
    
    # بروزرسانی فایل series_episodes_online.csv
    try:
        df_episodes['series_id'] = df_episodes['series_id'].map(id_mapping).fillna(df_episodes['series_id'])
        df_episodes.to_csv('series_episodes_online.csv', index=False, encoding='utf-8')
        print("✅ فایل series_episodes_online.csv بروزرسانی شد")
    except Exception as e:
        print(f"❌ خطا در بروزرسانی series_episodes: {e}")
    
    print(f"\n🎉 مهاجرت کامل شد!")
    print(f"   {len(new_processed)} سریال با ID جدید")
    print(f"   فایل‌های CSV بروزرسانی شدند")

if __name__ == "__main__":
    migrate_series_ids()
