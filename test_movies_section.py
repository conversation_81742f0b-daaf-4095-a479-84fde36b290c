import requests
from bs4 import BeautifulSoup
import re
from urllib.parse import urljoin

def test_movies_page(page_num):
    """تست صفحه مشخص در بخش فیلم‌ها"""
    base_url = 'https://www.myf2m.net'
    page_url = f"{base_url}/movies/page/{page_num}/"
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    try:
        print(f"🔍 بررسی: {page_url}")
        response = session.get(page_url, timeout=10)
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # جستجوی لینک‌های فیلم
            movie_links = set()
            all_links = soup.find_all('a', href=True)
            
            for link in all_links:
                href = link.get('href')
                if href and re.search(r'/\d{4,}/[^/]+/', href):
                    full_url = urljoin(base_url, href)
                    movie_links.add(full_url)
            
            print(f"✅ صفحه {page_num} موجود است - {len(movie_links)} فیلم پیدا شد")
            
            # نمایش چند نمونه لینک
            if movie_links:
                print("📋 نمونه لینک‌ها:")
                for i, link in enumerate(list(movie_links)[:3]):
                    print(f"  {i+1}. {link}")
            
            return True, len(movie_links)
        else:
            print(f"❌ صفحه {page_num} - کد خطا: {response.status_code}")
            return False, 0
            
    except Exception as e:
        print(f"❌ خطا در بررسی {page_url}: {e}")
        return False, 0

def find_last_movies_page():
    """پیدا کردن آخرین صفحه بخش فیلم‌ها"""
    print("🎬 جستجوی آخرین صفحه بخش فیلم‌ها...")
    
    # تست صفحات مختلف
    test_pages = [1, 50, 100, 150, 200, 217, 220, 250]
    
    last_valid_page = 1
    
    for page in test_pages:
        exists, movie_count = test_movies_page(page)
        if exists and movie_count > 0:
            last_valid_page = page
            print(f"✅ صفحه {page} معتبر است")
        else:
            print(f"❌ صفحه {page} خالی یا موجود نیست")
            # اگر صفحه 217 خالی بود، از همین جا متوقف شویم
            if page >= 217:
                break
    
    # جستجوی دقیق‌تر از آخرین صفحه معتبر
    if last_valid_page < 250:
        print(f"\n🎯 جستجوی دقیق از صفحه {last_valid_page}...")
        
        page = last_valid_page + 1
        consecutive_empty = 0
        
        while consecutive_empty < 3 and page <= 250:
            exists, movie_count = test_movies_page(page)
            if exists and movie_count > 0:
                last_valid_page = page
                consecutive_empty = 0
                print(f"✅ صفحه {page} معتبر - {movie_count} فیلم")
            else:
                consecutive_empty += 1
                print(f"❌ صفحه {page} خالی ({consecutive_empty}/3)")
            
            page += 1
    
    return last_valid_page

if __name__ == "__main__":
    print("🎬 تست بخش فیلم‌های سایت myf2m.net")
    print("=" * 50)
    
    # تست صفحه 217 مستقیماً
    print("📍 تست صفحه 217 در بخش فیلم‌ها:")
    exists_217, count_217 = test_movies_page(217)
    
    if exists_217:
        print(f"✅ صفحه 217 موجود است و {count_217} فیلم دارد")
    else:
        print("❌ صفحه 217 موجود نیست یا خالی است")
    
    print("\n" + "="*50)
    
    # تست چند صفحه اطراف 217
    print("📍 تست صفحات اطراف 217:")
    for page in [215, 216, 217, 218, 219]:
        exists, count = test_movies_page(page)
        if exists:
            print(f"✅ صفحه {page}: {count} فیلم")
        else:
            print(f"❌ صفحه {page}: خالی یا موجود نیست")
    
    # پیدا کردن آخرین صفحه
    print("\n" + "="*50)
    last_page = find_last_movies_page()
    print(f"\n🎉 آخرین صفحه معتبر بخش فیلم‌ها: {last_page}")
    
    # محاسبه تعداد کل فیلم‌ها
    total_movies = last_page * 20  # تقریباً 20 فیلم در هر صفحه
    print(f"📊 تعداد تقریبی فیلم‌ها: {total_movies:,}")
    print(f"⏱️  زمان تقریبی اسکرپ: {total_movies/3600:.1f} ساعت")
