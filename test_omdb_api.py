#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_omdb_api():
    """تست OMDb API"""
    
    # تنظیمات API
    api_key = '9b1ff0bb'
    base_url = 'http://www.omdbapi.com/'
    
    print("🔍 تست OMDb API")
    print("="*50)
    
    # تست 1: بررسی کلید API
    print("1️⃣ تست کلید API...")
    test_url = f"{base_url}?t=Breaking+Bad&apikey={api_key}"
    
    try:
        response = requests.get(test_url, timeout=10)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Response: {data.get('Response', 'نامشخص')}")
            
            if data.get('Response') == 'True':
                print("   ✅ کلید API معتبر است")
                print(f"   نام سریال: {data.get('Title', 'نامشخص')}")
                print(f"   سال: {data.get('Year', 'نامشخص')}")
                print(f"   امتیاز IMDb: {data.get('imdbRating', 'نامشخص')}")
            else:
                print(f"   ❌ خطا: {data.get('Error', 'نامشخص')}")
        else:
            print(f"   ❌ خطا در درخواست: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ خطا در اتصال: {e}")
    
    # تست 2: جستجوی سریال
    print(f"\n2️⃣ تست جستجوی سریال...")
    search_url = f"{base_url}?s=The+Bear&type=series&apikey={api_key}"
    
    try:
        response = requests.get(search_url, timeout=10)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Response: {data.get('Response', 'نامشخص')}")
            
            if data.get('Response') == 'True' and 'Search' in data:
                print(f"   ✅ {len(data['Search'])} نتیجه یافت شد")
                for i, result in enumerate(data['Search'][:3]):
                    print(f"     {i+1}. {result.get('Title', 'نامشخص')} ({result.get('Year', 'نامشخص')})")
                    print(f"        IMDb ID: {result.get('imdbID', 'نامشخص')}")
            else:
                print(f"   ❌ خطا: {data.get('Error', 'نامشخص')}")
        else:
            print(f"   ❌ خطا در درخواست: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ خطا در اتصال: {e}")
    
    # تست 3: دریافت اطلاعات با IMDb ID
    print(f"\n3️⃣ تست دریافت اطلاعات با IMDb ID...")
    imdb_id = "tt11198330"  # The Bear
    detail_url = f"{base_url}?i={imdb_id}&apikey={api_key}"
    
    try:
        response = requests.get(detail_url, timeout=10)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Response: {data.get('Response', 'نامشخص')}")
            
            if data.get('Response') == 'True':
                print("   ✅ اطلاعات دریافت شد")
                print(f"   نام: {data.get('Title', 'نامشخص')}")
                print(f"   تعداد فصل‌ها: {data.get('totalSeasons', 'نامشخص')}")
                print(f"   امتیاز: {data.get('imdbRating', 'نامشخص')}")
                print(f"   ژانر: {data.get('Genre', 'نامشخص')}")
            else:
                print(f"   ❌ خطا: {data.get('Error', 'نامشخص')}")
        else:
            print(f"   ❌ خطا در درخواست: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ خطا در اتصال: {e}")
    
    # تست 4: بررسی محدودیت API
    print(f"\n4️⃣ تست محدودیت API...")
    limit_url = f"{base_url}?apikey={api_key}"
    
    try:
        response = requests.get(limit_url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if 'Error' in data:
                if 'daily limit' in data['Error'].lower():
                    print("   ❌ محدودیت روزانه API به پایان رسیده")
                elif 'invalid api key' in data['Error'].lower():
                    print("   ❌ کلید API نامعتبر است")
                else:
                    print(f"   ❌ خطا: {data['Error']}")
            else:
                print("   ✅ API در دسترس است")
        else:
            print(f"   ❌ خطا در درخواست: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ خطا در اتصال: {e}")

if __name__ == "__main__":
    test_omdb_api()
