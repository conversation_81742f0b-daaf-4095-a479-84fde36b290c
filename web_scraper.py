import requests
from bs4 import BeautifulSoup
import csv
import time
import re
import os
import hashlib
import uuid
from urllib.parse import urljoin, urlparse
import json

class MovieScraper:
    def __init__(self, base_url='https://www.myf2m.net'):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.movies_data = []
        self.processed_urls = set()  # برای جلوگیری از اسکرپ مجدد
        self.current_page = 1

        # فایل‌های CSV
        self.movies_file = 'movies.csv'
        self.download_links_file = 'download_links.csv'
        self.genres_summaries_file = 'genres_summaries.csv'
        self.processed_urls_file = 'processed_urls.txt'

        # بارگذاری URL های پردازش شده
        self.load_processed_urls()

        self.quality_stats = {
            'total_processed': 0,
            'successful_extractions': 0,
            'missing_title': 0,
            'missing_year': 0,
            'missing_genre': 0,
            'missing_rating': 0,
            'missing_summary': 0,
            'missing_poster': 0,
            'missing_download_links': 0
        }

    def load_processed_urls(self):
        """بارگذاری URL های پردازش شده از فایل"""
        if os.path.exists(self.processed_urls_file):
            with open(self.processed_urls_file, 'r', encoding='utf-8') as f:
                self.processed_urls = set(line.strip() for line in f if line.strip())
            print(f"📂 بارگذاری {len(self.processed_urls)} URL پردازش شده")
        else:
            print("📂 فایل URL های پردازش شده وجود ندارد - شروع جدید")

    def save_processed_url(self, url):
        """ذخیره URL پردازش شده"""
        self.processed_urls.add(url)
        with open(self.processed_urls_file, 'a', encoding='utf-8') as f:
            f.write(url + '\n')

    def generate_movie_id(self, url):
        """تولید ID یکتا برای فیلم بر اساس URL"""
        return hashlib.md5(url.encode()).hexdigest()[:12]

    def initialize_csv_files(self):
        """ایجاد فایل‌های CSV اگر وجود نداشته باشند"""
        # فایل اصلی فیلم‌ها
        if not os.path.exists(self.movies_file):
            with open(self.movies_file, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                writer.writerow(['movie_id', 'title', 'year', 'rating', 'poster', 'url'])

        # فایل لینک‌های دانلود
        if not os.path.exists(self.download_links_file):
            with open(self.download_links_file, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                writer.writerow(['movie_id', 'link_url', 'quality', 'type', 'button_text'])

        # فایل ژانر و خلاصه
        if not os.path.exists(self.genres_summaries_file):
            with open(self.genres_summaries_file, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                writer.writerow(['movie_id', 'genre', 'summary'])

    def get_page(self, url):
        """Fetch page content with error handling"""
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            return BeautifulSoup(response.content, 'html.parser')
        except Exception as e:
            print(f"Error fetching {url}: {e}")
            return None
    
    def extract_movie_links(self, page_url):
        """Extract all movie page links from listing page"""
        soup = self.get_page(page_url)
        if not soup:
            return []
        
        movie_links = set()
        
        # Main strategy: Look for links with movie ID pattern /12345/movie-name/
        all_links = soup.find_all('a', href=True)
        for link in all_links:
            href = link.get('href')
            if href and re.search(r'/\d{4,}/[^/]+/', href):
                full_url = urljoin(self.base_url, href)
                movie_links.add(full_url)
        
        print(f"🎬 Found {len(movie_links)} movie links using pattern matching")
        
        # Additional strategy: Look for entry-title links
        title_links = soup.select('h2.entry-title a')
        for link in title_links:
            href = link.get('href')
            if href:
                full_url = urljoin(self.base_url, href)
                movie_links.add(full_url)
        
        # Additional strategy: Look for stretched-link class
        stretched_links = soup.select('a.stretched-link')
        for link in stretched_links:
            href = link.get('href')
            if href:
                full_url = urljoin(self.base_url, href)
                movie_links.add(full_url)
        
        return list(movie_links)
    
    def extract_year_from_title(self, title):
        """Extract year from movie title with improved patterns"""
        # Clean title first
        title = re.sub(r'دانلود فیلم\s*', '', title)
        title = re.sub(r'\s*بدون سانسور.*', '', title)
        
        # Look for 4-digit year patterns
        year_patterns = [
            r'\b(\d{4})\b',  # Any 4 digits as word boundary
            r'\((\d{4})\)',  # Year in parentheses
            r'-(\d{4})-',    # Year with dashes
            r'\s(\d{4})\s',  # Year with spaces
            r'(\d{4})$',     # Year at end of string
            r'^(\d{4})',     # Year at beginning
        ]
        
        for pattern in year_patterns:
            year_matches = re.findall(pattern, title)
            for year in year_matches:
                # Validate year range
                if 1900 <= int(year) <= 2030:
                    return year
        
        return ''
    
    def extract_year_from_url(self, url):
        """Extract year from URL if present"""
        # Look for year patterns in URL
        year_match = re.search(r'-(\d{4})/?$', url)
        if year_match:
            year = year_match.group(1)
            if 1900 <= int(year) <= 2030:
                return year
        return ''

    def extract_movie_details(self, movie_url):
        """Extract detailed movie information with real-time display"""
        soup = self.get_page(movie_url)
        if not soup:
            return None
        
        try:
            # Extract title
            title_selectors = ['h1', 'title', '.movie-title', '.film-title']
            title = ''
            for selector in title_selectors:
                title_elem = soup.select_one(selector)
                if title_elem:
                    title = title_elem.get_text().strip()
                    break
            
            # Extract year
            year = self.extract_year_from_title(title)
            
            # Extract genre
            genre = self.extract_genre(soup)
            
            # Extract rating
            rating = self.extract_rating(soup)
            
            # Extract summary
            summary = self.extract_summary(soup)
            
            # Extract poster
            poster = self.extract_poster(soup, movie_url)
            
            movie_data = {
                'title': title,
                'year': year,
                'genre': genre,
                'rating': rating,
                'summary': summary,
                'poster': poster,
                'url': movie_url
            }
            
            # Display extracted data
            self.display_movie_data(movie_data)
            
            # Get user confirmation
            confirmation = self.get_user_confirmation(movie_data)
            
            if confirmation == 'quit':
                return 'quit'
            elif confirmation:
                return movie_data
            else:
                return None
            
        except Exception as e:
            print(f"Error extracting details from {movie_url}: {e}")
            return None
    
    def extract_genre(self, soup):
        """Extract movie genre with improved selectors"""
        # Strategy 1: Look in meta tags
        genre_selectors = [
            'meta[name="keywords"]',
            'meta[property="article:tag"]'
        ]
        
        for selector in genre_selectors:
            elem = soup.select_one(selector)
            if elem:
                content = elem.get('content', '')
                if content:
                    return content
        
        # Strategy 2: Look for genre-specific elements
        genre_elements = soup.find_all(class_=re.compile(r'genre|category|tag'))
        if genre_elements:
            genres = [elem.get_text().strip() for elem in genre_elements if elem.get_text().strip()]
            if genres:
                return ', '.join(genres)
        
        # Strategy 3: Search in text content with Persian patterns
        text = soup.get_text()
        genre_patterns = [
            r'ژانر[:\s]*([^\n]+)',
            r'دسته[:\s]*([^\n]+)',
            r'Genre[:\s]*([^\n]+)',
            r'ژانرها[:\s]*([^\n]+)'
        ]
        
        for pattern in genre_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                genre_text = match.group(1).strip()
                # Clean up genre text
                genre_text = re.sub(r'[:\-\s]+$', '', genre_text)
                return genre_text
        
        return ''
    
    def extract_rating(self, soup):
        """Extract movie rating with improved selectors"""
        # Strategy 1: Look for IMDB rating in specific elements
        rating_selectors = [
            '.fw-500.fsz-16',  # Based on HTML structure seen
            '[class*="rating"]',
            '[class*="imdb"]',
            '.score'
        ]
        
        for selector in rating_selectors:
            elem = soup.select_one(selector)
            if elem:
                rating_text = elem.get_text().strip()
                rating_match = re.search(r'(\d+\.?\d*)', rating_text)
                if rating_match:
                    return rating_match.group(1)
        
        # Strategy 2: Look for SVG with IMDB icon followed by rating
        imdb_svgs = soup.find_all('svg')
        for svg in imdb_svgs:
            use_elem = svg.find('use')
            if use_elem and 'imdb' in str(use_elem.get('xlink:href', '')):
                # Find next sibling with rating
                parent = svg.find_parent()
                if parent:
                    rating_elem = parent.find(class_='fw-500')
                    if rating_elem:
                        rating_text = rating_elem.get_text().strip()
                        rating_match = re.search(r'(\d+\.?\d*)', rating_text)
                        if rating_match:
                            return rating_match.group(1)
        
        # Strategy 3: Search in text content
        text = soup.get_text()
        rating_patterns = [
            r'(\d+\.?\d*)/10',
            r'imdb[:\s]*(\d+\.?\d*)',
            r'امتیاز[:\s]*(\d+\.?\d*)',
            r'rating[:\s]*(\d+\.?\d*)'
        ]
        
        for pattern in rating_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return ''
    
    def extract_summary(self, soup):
        """Extract movie summary with improved selectors"""
        # Strategy 1: Look for common summary selectors
        summary_selectors = [
            '.summary', '.description', '.plot', '.story',
            '[class*="summary"]', '[class*="description"]',
            '[class*="plot"]', '[class*="story"]',
            '.entry-content p'
        ]
        
        for selector in summary_selectors:
            elem = soup.select_one(selector)
            if elem:
                summary = elem.get_text().strip()
                if len(summary) > 50:
                    return summary
        
        # Strategy 2: Look for paragraphs with Persian content
        paragraphs = soup.find_all('p')
        for p in paragraphs:
            text = p.get_text().strip()
            # Look for paragraphs with Persian keywords and sufficient length
            if len(text) > 100 and any(word in text for word in ['داستان', 'خلاصه', 'فیلم', 'در', 'که', 'است']):
                return text
        
        # Strategy 3: Look in div elements
        divs = soup.find_all('div')
        for div in divs:
            text = div.get_text().strip()
            if len(text) > 150 and len(text) < 1000 and any(word in text for word in ['داستان', 'خلاصه']):
                return text
        
        return ''
    
    def extract_poster(self, soup, base_url):
        """Extract movie poster URL"""
        poster_selectors = [
            'img[class*="poster"]',
            'img[class*="cover"]',
            '.movie-poster img',
            '.film-poster img',
            'video[poster]'
        ]
        
        for selector in poster_selectors:
            if 'video' in selector:
                elem = soup.select_one(selector)
                if elem and elem.get('poster'):
                    return urljoin(base_url, elem.get('poster'))
            else:
                elem = soup.select_one(selector)
                if elem and elem.get('src'):
                    return urljoin(base_url, elem.get('src'))
        
        return ''
    
    def extract_download_links(self, soup):
        """Extract direct download links based on kinguploadf2m pattern"""
        download_links = []
        
        try:
            # Look for all links
            all_links = soup.find_all('a', href=True)
            
            for link in all_links:
                href = link.get('href', '')
                text = link.get_text().strip()
                
                # Check for kinguploadf2m domain (direct download links)
                if 'kinguploadf2m' in href and '.mkv' in href:
                    # Extract quality from URL
                    quality = 'Unknown'
                    if '1080p' in href:
                        quality = '1080p'
                    elif '720p' in href:
                        quality = '720p'
                    elif '480p' in href:
                        quality = '480p'
                    
                    # Extract format info
                    format_info = []
                    if 'WEB-DL' in href:
                        format_info.append('WEB-DL')
                    if 'BluRay' in href:
                        format_info.append('BluRay')
                    if 'x265' in href:
                        format_info.append('x265')
                    if '10bit' in href:
                        format_info.append('10bit')
                    if 'Farsi' in href:
                        format_info.append('Farsi Dubbed')
                    
                    quality_full = f"{quality} {' '.join(format_info)}".strip()
                    
                    download_links.append({
                        'url': href,
                        'quality': quality_full,
                        'type': 'direct_download',
                        'button_text': text
                    })
                
                # Check for streaming links (پخش آنلاین)
                elif 'پخش آنلاین' in text and href:
                    download_links.append({
                        'url': href,
                        'quality': 'Stream',
                        'type': 'stream',
                        'button_text': text
                    })
                
                # Check for other download buttons
                elif 'دانلود مستقیم' in text and href:
                    download_links.append({
                        'url': href,
                        'quality': 'Direct Download',
                        'type': 'download',
                        'button_text': text
                    })
            
            # Remove duplicates based on URL
            seen_urls = set()
            unique_links = []
            for link in download_links:
                if link['url'] not in seen_urls:
                    seen_urls.add(link['url'])
                    unique_links.append(link)
            
            return unique_links
            
        except Exception as e:
            print(f"Error extracting download links: {e}")
            return []

    def save_movie_data_to_csv(self, movie_data):
        """ذخیره داده‌های فیلم در فایل‌های CSV جداگانه"""
        movie_id = self.generate_movie_id(movie_data.get('url', ''))

        # ذخیره اطلاعات اصلی فیلم
        with open(self.movies_file, 'a', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            writer.writerow([
                movie_id,
                movie_data.get('title', ''),
                movie_data.get('year', ''),
                movie_data.get('rating', ''),
                movie_data.get('poster', ''),
                movie_data.get('url', '')
            ])

        # ذخیره لینک‌های دانلود
        download_links = movie_data.get('download_links', [])
        if download_links:
            with open(self.download_links_file, 'a', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                for link in download_links:
                    writer.writerow([
                        movie_id,
                        link.get('url', ''),
                        link.get('quality', ''),
                        link.get('type', ''),
                        link.get('button_text', '')
                    ])

        # ذخیره ژانر و خلاصه
        with open(self.genres_summaries_file, 'a', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            writer.writerow([
                movie_id,
                movie_data.get('genre', ''),
                movie_data.get('summary', '')
            ])

    def display_movie_data(self, movie_data):
        """Display extracted movie data in real-time"""
        print("\n" + "="*80)
        print(f"🎬 MOVIE EXTRACTED #{len(self.movies_data) + 1}")
        print("="*80)
        print(f"Title: {movie_data.get('title', 'N/A')}")
        print(f"Year: {movie_data.get('year', 'N/A')}")
        print(f"Genre: {movie_data.get('genre', 'N/A')}")
        print(f"Rating: {movie_data.get('rating', 'N/A')}")
        print(f"Summary: {movie_data.get('summary', 'N/A')[:100]}...")
        print(f"Poster: {movie_data.get('poster', 'N/A')}")
        
        # Display download links
        download_links = movie_data.get('download_links', [])
        if download_links:
            print(f"\n📥 DOWNLOAD LINKS ({len(download_links)} found):")
            for i, link in enumerate(download_links):
                print(f"  {i+1}. {link['quality']} ({link['type']})")
                print(f"     Button: {link['button_text']}")
                print(f"     URL: {link['url'][:80]}...")
                print()
        else:
            print("\n📥 DOWNLOAD LINKS: None found")
        
        print(f"URL: {movie_data.get('url', 'N/A')}")
        print("="*80)
        
        # Quality check
        self.check_data_quality(movie_data)
        self.display_quality_stats()
    
    def check_data_quality(self, movie_data):
        """Check quality of extracted data"""
        self.quality_stats['total_processed'] += 1
        
        if movie_data.get('title'):
            self.quality_stats['successful_extractions'] += 1
        else:
            self.quality_stats['missing_title'] += 1
            
        if not movie_data.get('year'):
            self.quality_stats['missing_year'] += 1
            
        if not movie_data.get('genre'):
            self.quality_stats['missing_genre'] += 1
            
        if not movie_data.get('rating'):
            self.quality_stats['missing_rating'] += 1
            
        if not movie_data.get('summary'):
            self.quality_stats['missing_summary'] += 1
            
        if not movie_data.get('poster'):
            self.quality_stats['missing_poster'] += 1
            
        if not movie_data.get('download_links'):
            self.quality_stats['missing_download_links'] += 1
    
    def display_quality_stats(self):
        """Display current quality statistics"""
        stats = self.quality_stats
        total = stats['total_processed']
        
        if total == 0:
            return
        
        print(f"\n📊 QUALITY STATS:")
        print(f"Total Processed: {total}")
        print(f"Success Rate: {(stats['successful_extractions']/total)*100:.1f}%")
        print(f"Missing Data:")
        print(f"  - Title: {stats['missing_title']} ({(stats['missing_title']/total)*100:.1f}%)")
        print(f"  - Year: {stats['missing_year']} ({(stats['missing_year']/total)*100:.1f}%)")
        print(f"  - Genre: {stats['missing_genre']} ({(stats['missing_genre']/total)*100:.1f}%)")
        print(f"  - Rating: {stats['missing_rating']} ({(stats['missing_rating']/total)*100:.1f}%)")
        print(f"  - Summary: {stats['missing_summary']} ({(stats['missing_summary']/total)*100:.1f}%)")
        print(f"  - Poster: {stats['missing_poster']} ({(stats['missing_poster']/total)*100:.1f}%)")
        print(f"  - Download Links: {stats['missing_download_links']} ({(stats['missing_download_links']/total)*100:.1f}%)")
        print("-"*50)
    
    def get_user_confirmation(self, movie_data):
        """Ask user to confirm data before saving"""
        while True:
            choice = input("\n🤔 Save this movie? (y/n/e/q): ").lower().strip()
            
            if choice == 'y':
                return True
            elif choice == 'n':
                print("❌ Skipping this movie...")
                return False
            elif choice == 'e':
                self.edit_movie_data(movie_data)
                return True
            elif choice == 'q':
                print("🛑 Stopping scraper...")
                return 'quit'
            else:
                print("Please enter: y (yes), n (no), e (edit), q (quit)")
    
    def edit_movie_data(self, movie_data):
        """Allow user to edit movie data"""
        print("\n✏️  EDIT MODE - Press Enter to keep current value")
        
        fields = ['title', 'year', 'genre', 'rating', 'summary', 'poster']
        
        for field in fields:
            current_value = movie_data.get(field, '')
            print(f"\nCurrent {field}: {current_value}")
            new_value = input(f"New {field}: ").strip()
            
            if new_value:
                movie_data[field] = new_value
                print(f"✅ Updated {field}")
    
    def scrape_movies_unlimited(self, start_page=1, interactive=False):
        """اسکرپ نامحدود تمام صفحات سایت"""
        print(f"🚀 شروع اسکرپ نامحدود از {self.base_url}")
        print(f"صفحه شروع: {start_page}")

        # ایجاد فایل‌های CSV
        self.initialize_csv_files()

        page = start_page
        consecutive_empty_pages = 0
        max_empty_pages = 3  # توقف بعد از 3 صفحه خالی متوالی
        max_page = 217  # حداکثر صفحه بخش فیلم‌ها

        while consecutive_empty_pages < max_empty_pages and page <= max_page:
            print(f"\n📄 پردازش صفحه {page}...")

            # فقط بخش فیلم‌ها
            page_urls = [
                f"{self.base_url}/movies/page/{page}/",
                f"{self.base_url}/movies/" if page == 1 else None
            ]

            page_urls = [url for url in page_urls if url is not None]

            movie_links = []
            for page_url in page_urls:
                print(f"🔍 بررسی URL: {page_url}")
                links = self.extract_movie_links(page_url)
                if links:
                    movie_links.extend(links)
                    print(f"✅ {len(links)} فیلم پیدا شد در {page_url}")
                    break
                else:
                    print(f"❌ فیلمی پیدا نشد در {page_url}")

            if not movie_links:
                consecutive_empty_pages += 1
                print(f"❌ صفحه {page} خالی است ({consecutive_empty_pages}/{max_empty_pages})")
                page += 1
                continue
            else:
                consecutive_empty_pages = 0

            # حذف تکراری‌ها
            movie_links = list(set(movie_links))
            print(f"✅ مجموع فیلم‌های یکتا: {len(movie_links)}")

            # فیلتر کردن فیلم‌هایی که قبلاً پردازش شده‌اند
            new_movie_links = [url for url in movie_links if url not in self.processed_urls]
            print(f"🆕 فیلم‌های جدید: {len(new_movie_links)}")

            # پردازش فیلم‌ها
            for i, movie_url in enumerate(new_movie_links):
                print(f"\n🎯 پردازش فیلم {i+1}/{len(new_movie_links)} از صفحه {page}")
                print(f"URL: {movie_url}")

                try:
                    movie_data = self.extract_movie_details_silent(movie_url)
                    if movie_data:
                        self.save_movie_data_to_csv(movie_data)
                        self.save_processed_url(movie_url)
                        print(f"✅ فیلم ذخیره شد: {movie_data.get('title', 'نامشخص')}")
                    else:
                        print(f"❌ خطا در استخراج داده‌های فیلم")
                        self.save_processed_url(movie_url)  # برای جلوگیری از تلاش مجدد
                except Exception as e:
                    print(f"❌ خطا در پردازش {movie_url}: {e}")
                    self.save_processed_url(movie_url)

                time.sleep(1)  # احترام به سرور

            page += 1
            print(f"📊 صفحه {page-1} کامل شد. تعداد کل URL های پردازش شده: {len(self.processed_urls)}")

        if page > max_page:
            print(f"\n🎉 اسکرپ کامل شد! تمام {max_page} صفحه بخش فیلم‌ها پردازش شد")
        else:
            print(f"\n🎉 اسکرپ کامل شد! آخرین صفحه پردازش شده: {page-1}")
        print(f"📊 مجموع URL های پردازش شده: {len(self.processed_urls)}")

    def scrape_movies(self, start_page=1, max_pages=5, interactive=True):
        """متد قدیمی برای سازگاری"""
        if max_pages == -1:  # اگر نامحدود خواسته شود
            return self.scrape_movies_unlimited(start_page, interactive)

        print(f"🚀 Starting to scrape movies from {self.base_url}")
        print(f"Interactive mode: {'ON' if interactive else 'OFF'}")

        for page in range(start_page, start_page + max_pages):
            print(f"\n📄 Processing page {page}...")

            # Try different page URL patterns
            page_urls = [
                f"{self.base_url}/movies/page/{page}/",
                f"{self.base_url}/movies/",  # For page 1
                f"{self.base_url}/page/{page}/",
                f"{self.base_url}/"  # For homepage
            ]

            movie_links = []
            for page_url in page_urls:
                print(f"🔍 Trying URL: {page_url}")
                links = self.extract_movie_links(page_url)
                if links:
                    movie_links.extend(links)
                    print(f"✅ Found {len(links)} movies at {page_url}")
                    break
                else:
                    print(f"❌ No movies found at {page_url}")

            if not movie_links:
                print(f"❌ No movies found on page {page}")
                continue

            # Remove duplicates
            movie_links = list(set(movie_links))
            print(f"✅ Total unique movies found: {len(movie_links)}")

            # Process movies (nncreaseafrem frot)20
            for i, movie_url in enumerate(movie_links[:20]):
                print(f"\n🎯 Processing movie {i+1}/{min(len(movie_links), 20)}")
                print(f"URL: {movie_url}")

                if interactive:
                    movie_data = self.extract_movie_details(movie_url)
                    if movie_data == 'quit':
                        print("🛑 User requested to quit")
                        return
                    elif movie_data:
                        self.movies_data.append(movie_data)
                else:
                    movie_data = self.extract_movie_details_silent(movie_url)
                    if movie_data:
                        self.movies_data.append(movie_data)
                        self.display_movie_data(movie_data)

                time.sleep(1)  # Be respectful to the server

            # Only process first page for now
            break
        
        print(f"\n🎉 Total movies extracted: {len(self.movies_data)}")
        self.display_final_stats()
    
    def display_final_stats(self):
        """Display final extraction statistics"""
        print("\n" + "="*80)
        print("📈 FINAL EXTRACTION REPORT")
        print("="*80)
        self.display_quality_stats()
        
        if self.movies_data:
            print(f"\n✅ Successfully extracted {len(self.movies_data)} movies")
        else:
            print("\n❌ No movies were extracted")
    
    def save_to_csv(self, filename='movies_data.csv'):
        """Save extracted data to CSV"""
        if not self.movies_data:
            print("No data to save!")
            return
        
        fieldnames = ['title', 'year', 'genre', 'rating', 'summary', 'poster', 'download_links', 'url']
        
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for movie in self.movies_data:
                # Convert download_links to string for CSV
                movie_copy = movie.copy()
                if 'download_links' in movie_copy:
                    movie_copy['download_links'] = json.dumps(movie_copy['download_links'], ensure_ascii=False)
                writer.writerow(movie_copy)
        
        print(f"Saved {len(self.movies_data)} movies to {filename}")

    def extract_movie_details_silent(self, movie_url):
        """Extract movie details without user interaction"""
        soup = self.get_page(movie_url)
        if not soup:
            return None
        
        try:
            # Extract title
            title_selectors = [
                'h1.entry-title',
                'h1', 
                'title',
                '.movie-title', 
                '.film-title'
            ]
            title = ''
            for selector in title_selectors:
                title_elem = soup.select_one(selector)
                if title_elem:
                    title = title_elem.get_text().strip()
                    # Clean Persian movie title
                    title = re.sub(r'دانلود فیلم\s*', '', title)
                    title = re.sub(r'\s*بدون سانسور.*', '', title)
                    break
            
            # Extract year - try multiple methods
            year = self.extract_year_from_title(title)
            if not year:
                year = self.extract_year_from_url(movie_url)
            
            # Extract genre
            genre = self.extract_genre(soup)
            
            # Extract rating
            rating = self.extract_rating(soup)
            
            # Extract summary
            summary = self.extract_summary(soup)
            
            # Extract poster
            poster = self.extract_poster(soup, movie_url)
            
            # Extract download links
            download_links = self.extract_download_links(soup)
            
            movie_data = {
                'title': title,
                'year': year,
                'genre': genre,
                'rating': rating,
                'summary': summary,
                'poster': poster,
                'download_links': download_links,
                'url': movie_url
            }
            
            # Quality check
            self.check_data_quality(movie_data)
            
            return movie_data
            
        except Exception as e:
            print(f"Error extracting details from {movie_url}: {e}")
            return None

    def save_movies_with_links_only(self, filename='movies_with_links.csv'):
        """Save only movies that have download links"""
        movies_with_links = [movie for movie in self.movies_data if movie.get('download_links')]
        
        if not movies_with_links:
            print("No movies with download links found!")
            return
        
        fieldnames = ['title', 'year', 'genre', 'rating', 'summary', 'poster', 'download_links', 'url']
        
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for movie in movies_with_links:
                movie_copy = movie.copy()
                if 'download_links' in movie_copy:
                    movie_copy['download_links'] = json.dumps(movie_copy['download_links'], ensure_ascii=False)
                writer.writerow(movie_copy)
        
        print(f"✅ Saved {len(movies_with_links)} movies with download links to {filename}")
        print(f"📊 Total movies processed: {len(self.movies_data)}")
        print(f"📥 Movies with links: {len(movies_with_links)} ({(len(movies_with_links)/len(self.movies_data))*100:.1f}%)")

    def save_movies_with_links_only(self, filename='movies_with_links.csv'):
        """Save only movies that have download links"""
        movies_with_links = [movie for movie in self.movies_data if movie.get('download_links')]
        
        if not movies_with_links:
            print("No movies with download links found!")
            return
        
        fieldnames = ['title', 'year', 'genre', 'rating', 'summary', 'poster', 'download_links', 'url']
        
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for movie in movies_with_links:
                movie_copy = movie.copy()
                if 'download_links' in movie_copy:
                    movie_copy['download_links'] = json.dumps(movie_copy['download_links'], ensure_ascii=False)
                writer.writerow(movie_copy)
        
        print(f"✅ Saved {len(movies_with_links)} movies with download links to {filename}")
        print(f"📊 Total movies processed: {len(self.movies_data)}")
        print(f"📥 Movies with links: {len(movies_with_links)} ({(len(movies_with_links)/len(self.movies_data))*100:.1f}%)")

if __name__ == "__main__":
    scraper = MovieScraper()

    print("🎬 اسکرپر فیلم - نسخه جدید")
    print("=" * 50)
    print("1. اسکرپ نامحدود تمام صفحات")
    print("2. اسکرپ محدود (تست)")
    print("3. نمایش آمار فایل‌ها")

    choice = input("\nانتخاب کنید (1-3): ").strip()

    if choice == "1":
        print("\n🚀 شروع اسکرپ نامحدود...")
        print("⚠️  برای توقف از Ctrl+C استفاده کنید")
        try:
            scraper.scrape_movies_unlimited(start_page=1)
        except KeyboardInterrupt:
            print("\n⏹️  اسکرپ توسط کاربر متوقف شد")
            print(f"📊 تعداد URL های پردازش شده: {len(scraper.processed_urls)}")

    elif choice == "2":
        pages = input("تعداد صفحات برای تست (پیش‌فرض: 2): ").strip()
        pages = int(pages) if pages.isdigit() else 2

        print(f"\n🧪 شروع تست با {pages} صفحه...")
        scraper.scrape_movies(start_page=1, max_pages=pages, interactive=False)

        if scraper.movies_data:
            scraper.save_to_csv('test_movies.csv')
            print(f"\n✅ تست کامل شد! {len(scraper.movies_data)} فیلم در test_movies.csv ذخیره شد")
        else:
            print("\n❌ هیچ فیلمی اسکرپ نشد")

    elif choice == "3":
        print("\n📊 آمار فایل‌ها:")
        files_info = [
            (scraper.movies_file, "فیلم‌ها"),
            (scraper.download_links_file, "لینک‌های دانلود"),
            (scraper.genres_summaries_file, "ژانر و خلاصه"),
            (scraper.processed_urls_file, "URL های پردازش شده")
        ]

        for filename, description in files_info:
            if os.path.exists(filename):
                if filename.endswith('.txt'):
                    with open(filename, 'r', encoding='utf-8') as f:
                        count = len([line for line in f if line.strip()])
                else:
                    with open(filename, 'r', encoding='utf-8-sig') as f:
                        count = len(f.readlines()) - 1  # منهای header
                print(f"  {description}: {count} رکورد")
            else:
                print(f"  {description}: فایل وجود ندارد")

    else:
        print("❌ انتخاب نامعتبر!")

    print("\n🎬 پایان برنامه")
