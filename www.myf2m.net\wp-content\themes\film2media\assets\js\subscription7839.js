jQuery(function ($) {
    $(document).on("click", ".check_discount", (function (t) {
        let n = $(this), code = $("#discount_code").val(),
            subscriptionID = $("input[name=subscriptionID]:checked").val(),
            global_user = $('.global_user:checked').val();
        $.ajax({
            url: ajax_var.ajaxurl,
            type: "POST",
            data: {action: "check_discount_Ajax", code: code, subs_id: subscriptionID, global_user: global_user},
            beforeSend: function () {
                n.append('<img style="margin-right:10px;" class="loading_src" src="' + ajax_var.loading + '" width="20" height="20">');
            },
            success: function (response) {
                console.log(response);
                n.find(".loading_src").remove();
                if (response.res == 1) {
                    if ($('.global_user:checked').val() == 1) {
                        $("#buy_subscription .amount").html(response.usd_amount + " دلار");
                        $("#buy_subscription .currency").html("(" + response.formated_amount + " تومان" + ")");
                    } else {
                        $("#buy_subscription .amount").html(response.formated_amount);
                        $("#buy_subscription .currency").html("تومان");
                    }
                }
                $("#discount_res").html(response.msg);
            },
            error: function (jqXHR, textStatus, errorThrown) {
                n.find(".loading_src").remove();
                console.log(errorThrown);
            }
        });
    }));

    function check_gate() {
    }

    function checkGlobalCheckboxStatus() {
        $('label.item_subscription').each(function () {
            var price = $(this).data('price');
            var price_formated = $(this).data('price_formated');
            var usd_price = $(this).data('usd_price');
            var usdf_price = $(this).data('usdf_price');
            var usdf_irt_formated = $(this).data('usdf_irt_formated');
            if ($('.global_user:checked').val() == 1) {
                $(this).find('.amount').html(usdf_price);
                $(this).find('.currency').html("دلار");
            } else {
                $(this).find('.amount').html(price_formated);
                $(this).find('.currency').html("تومان");
            }
        });
    }

    $('.global_user').change(function () {
        checkGlobalCheckboxStatus();
        check_active_label();
    });
    $('input[name="subscriptionID"]').on('change', function () {
        check_gate();
        check_active_label();
    });

    function check_active_label() {
        var active_label = $('label.item_subscription.active');
        var price = active_label.data('price');
        var price_formated = active_label.data('price_formated');
        var usd_price = active_label.data('usd_price');
        var usdf_price = active_label.data('usdf_price');
        var usdf_irt_formated = active_label.data('usdf_irt_formated');
        if ($('.global_user:checked').val() == 1) {
            $("#buy_subscription .amount").html(usdf_price);
            $("#buy_subscription .currency").html("دلار");
        } else {
            $("#buy_subscription .amount").html(price_formated);
            $("#buy_subscription .currency").html("تومان");
        }
    }

    $(document).on("click", ".item_subscription", (function () {
        $(".item_subscription").removeClass("active"), $(this).addClass("active");
    }));
    if (typeof showDialogMessage != 'function') {
        function showDialogMessage(title, body) {
            $('.wpp-modal .m-title').html(title);
            $('.wpp-modal .modal-body').html(body);
            $('.wpp-modal').show();
        }

        $('.wpp-modal .close').on('click', function (event) {
            $('.wpp-modal').hide();
            $('.wpp-modal .modal-body').html("");
            $('.wpp-modal .m-title').html("");
        });
        $(document).on('click', '.wpp-modal .btn-close', function (event) {
            $('.wpp-modal').hide();
            $('.wpp-modal .modal-body').html("");
            $('.wpp-modal .m-title').html("");
        });
        $(document).on('click', function (event) {
            if (event.target.id != undefined && event.target.id == 'wpp_Modal') {
                $('.wpp-modal').hide();
                $('.wpp-modal .modal-body').html("");
                $('.wpp-modal .m-title').html("");
            }
        });
    }
    try {
        check_gate();
        if (global_var.html_msg_subs.length > 0) {
            showDialogMessage("هشدار", global_var.html_msg_subs);
        }
        checkGlobalCheckboxStatus();
        check_active_label();
    } catch (err) {
        console.log(err.message);
    }
});


jQuery(function ($) {
    $(document).on("click", ".check_discount", (function (t) {
            let n = $(this)
                , code = $("#discount_code").val()
                , subscriptionID = $("input[name=subscriptionID]:checked").val()
                , global_user = $('.global_user:checked').val();
            $.ajax({
                url: ajax_var.ajaxurl,
                type: "POST",
                data: {
                    action: "check_discount_Ajax",
                    code: code,
                    subs_id: subscriptionID,
                    global_user: global_user
                },
                beforeSend: function () {
                    n.append('<img style="margin-right:10px;" class="loading_src" src="' + ajax_var.loading + '" width="20" height="20">');
                },
                success: function (response) {
                    console.log(response);
                    n.find(".loading_src").remove();
                    if (response.res == 1) {
                        if ($('.global_user:checked').val() == 1) {
                            $("#buy_subscription .amount").html(response.usd_amount );
                            $("#buy_subscription .currency").html("دلار");
                        } else {
                            $("#buy_subscription .amount").html(response.formated_amount);
                            $("#buy_subscription .currency").html("تومان");
                        }
                    }
                    $("#discount_res").html(response.msg);
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    n.find(".loading_src").remove();
                    console.log(errorThrown);
                }
            });
        }
    ));

    function check_gate() {
    }

    function checkGlobalCheckboxStatus() {
        $('label.item_subscription').each(function () {
            var price = $(this).data('price');
            var price_formated = $(this).data('price_formated');
            var usd_price = $(this).data('usd_price');
            var usdf_price = $(this).data('usdf_price');
            var usdf_irt_formated = $(this).data('usdf_irt_formated');
            if ($('.global_user:checked').val() == 1) {
                $(this).find('.amount').html(usdf_price);
                $(this).find('.currency').html("دلار");
            } else {
                $(this).find('.amount').html(price_formated);
                $(this).find('.currency').html("تومان");
            }
        });
    }

    $('.global_user').change(function () {
        checkGlobalCheckboxStatus();
        check_active_label();
    });
    $('input[name="subscriptionID"]').on('change', function () {
        check_gate();
        check_active_label();
    });

    function check_active_label() {
        var active_label = $('label.item_subscription.active');
        var price = active_label.data('price');
        var price_formated = active_label.data('price_formated');
        var usd_price = active_label.data('usd_price');
        var usdf_price = active_label.data('usdf_price');
        var usdf_irt_formated = active_label.data('usdf_irt_formated');
        if ($('.global_user:checked').val() == 1) {
            $("#buy_subscription .amount").html(usdf_price);
            $("#buy_subscription .currency").html("دلار");
        } else {
            $("#buy_subscription .amount").html(price_formated);
            $("#buy_subscription .currency").html("تومان");
        }
    }

    $(document).on("click", ".item_subscription", (function () {
            $(".item_subscription").removeClass("active"),
                $(this).addClass("active");
        }
    ));
    if (typeof showDialogMessage != 'function') {
        function showDialogMessage(title, body) {
            $('.wpp-modal .m-title').html(title);
            $('.wpp-modal .modal-body').html(body);
            $('.wpp-modal').show();
        }

        $('.wpp-modal .close').on('click', function (event) {
            $('.wpp-modal').hide();
            $('.wpp-modal .modal-body').html("");
            $('.wpp-modal .m-title').html("");
        });
        $(document).on('click', '.wpp-modal .btn-close', function (event) {
            $('.wpp-modal').hide();
            $('.wpp-modal .modal-body').html("");
            $('.wpp-modal .m-title').html("");
        });
        $(document).on('click', function (event) {
            if (event.target.id != undefined && event.target.id == 'wpp_Modal') {
                $('.wpp-modal').hide();
                $('.wpp-modal .modal-body').html("");
                $('.wpp-modal .m-title').html("");
            }
        });
    }
    try {
        check_gate();
        if (global_var.html_msg_subs.length > 0) {
            showDialogMessage("هشدار", global_var.html_msg_subs);
        }
        checkGlobalCheckboxStatus();
        check_active_label();
    } catch (err) {
        console.log(err.message);
    }
});



$(document).ready(function() {
    $('#policy_url_check').change(function() {
        if ($(this).is(':checked')) {
            $('#buy_subscription').removeClass('disabled').prop('disabled', false);
        } else {
            $('#buy_subscription').addClass('disabled').prop('disabled', true);
        }
    });
});



