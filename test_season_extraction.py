#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from webscraper_serial_online import OnlineSeriesScraper

def test_season_extraction():
    """تست استخراج فصل‌ها"""
    scraper = OnlineSeriesScraper()
    
    # تست متن‌های مختلف
    test_texts = [
        "قسمت 6 فصل 2 دوبله فارسی",
        "قسمت 10 فصل 4 دوبله فارسی", 
        "فصل 3 قسمت 5",
        "S02E08",
        "Season 4 Episode 10",
        "قسمت 01",
        "The Bear S04E01"
    ]
    
    print("🔍 تست استخراج فصل و قسمت:")
    print("="*50)
    
    for text in test_texts:
        season, episode = scraper.extract_season_and_episode(text)
        print(f"'{text}' -> فصل {season}, قسمت {episode}")
    
    print("\n🎬 تست استخراج از The Bear:")
    print("="*50)
    
    # تست واقعی The Bear
    url = 'https://www.myf2m.net/series/the-bear-series/'
    response = scraper.make_request(url)
    
    if response:
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # جستجوی متن‌های حاوی فصل و قسمت
        episode_texts = soup.find_all(string=lambda text: text and 
                                    ('قسمت' in text and 'فصل' in text))
        
        print(f"متن‌های یافت شده: {len(episode_texts)}")
        
        seasons_found = set()
        for text in episode_texts[:10]:  # فقط 10 تای اول
            clean_text = ' '.join(text.strip().split())
            season, episode = scraper.extract_season_and_episode(clean_text)
            if episode > 0:
                seasons_found.add(season)
                print(f"'{clean_text[:60]}...' -> فصل {season}, قسمت {episode}")
        
        print(f"\n📊 فصل‌های یافت شده: {sorted(seasons_found)}")

if __name__ == "__main__":
    test_season_extraction()
