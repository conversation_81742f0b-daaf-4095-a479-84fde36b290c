#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup
import time

def check_page_content(page_num):
    """بررسی محتوای یک صفحه خاص"""
    url = f"https://www.myf2m.com/movies/page/{page_num}/"
    
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # پیدا کردن فیلم‌ها
            movies = soup.find_all('article', class_='item movies')
            
            print(f"صفحه {page_num}: {len(movies)} فیلم پیدا شد")
            
            if movies:
                # نمایش اولین فیلم
                first_movie = movies[0]
                title_elem = first_movie.find('h3')
                if title_elem:
                    title = title_elem.get_text(strip=True)
                    print(f"  اولین فیلم: {title}")
            
            return len(movies)
        else:
            print(f"صفحه {page_num}: خطا {response.status_code}")
            return 0
            
    except Exception as e:
        print(f"صفحه {page_num}: خطا - {str(e)}")
        return 0

def main():
    print("🔍 بررسی وضعیت صفحات...")
    
    # بررسی چند صفحه اول
    for page in [1, 2, 3, 4, 5]:
        count = check_page_content(page)
        time.sleep(1)  # تاخیر برای جلوگیری از محدودیت
    
    print("\n" + "="*50)
    
    # بررسی چند صفحه آخر
    for page in [215, 216, 217]:
        count = check_page_content(page)
        time.sleep(1)

if __name__ == "__main__":
    main()
