import requests
from bs4 import BeautifulSoup
import re
from urllib.parse import urljoin

def test_page_exists(page_num):
    """تست وجود صفحه مشخص"""
    base_url = 'https://www.myf2m.net'
    
    # الگوهای مختلف URL
    page_urls = [
        f"{base_url}/movies/page/{page_num}/",
        f"{base_url}/page/{page_num}/",
    ]
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    for page_url in page_urls:
        try:
            print(f"🔍 بررسی: {page_url}")
            response = session.get(page_url, timeout=10)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # جستجوی لینک‌های فیلم
                movie_links = set()
                all_links = soup.find_all('a', href=True)
                
                for link in all_links:
                    href = link.get('href')
                    if href and re.search(r'/\d{4,}/[^/]+/', href):
                        full_url = urljoin(base_url, href)
                        movie_links.add(full_url)
                
                print(f"✅ صفحه {page_num} موجود است - {len(movie_links)} فیلم پیدا شد")
                return True, len(movie_links)
            else:
                print(f"❌ صفحه {page_num} - کد خطا: {response.status_code}")
                
        except Exception as e:
            print(f"❌ خطا در بررسی {page_url}: {e}")
    
    return False, 0

def find_last_page():
    """پیدا کردن آخرین صفحه موجود"""
    print("🔍 جستجوی آخرین صفحه...")
    
    # تست صفحات مختلف
    test_pages = [1, 10, 50, 100, 150, 200, 217, 250, 300]
    
    last_valid_page = 1
    
    for page in test_pages:
        exists, movie_count = test_page_exists(page)
        if exists and movie_count > 0:
            last_valid_page = page
            print(f"✅ صفحه {page} معتبر است")
        else:
            print(f"❌ صفحه {page} خالی یا موجود نیست")
            break
    
    # جستجوی دقیق‌تر از آخرین صفحه معتبر
    print(f"\n🎯 جستجوی دقیق از صفحه {last_valid_page}...")
    
    page = last_valid_page
    consecutive_empty = 0
    
    while consecutive_empty < 3:
        exists, movie_count = test_page_exists(page)
        if exists and movie_count > 0:
            last_valid_page = page
            consecutive_empty = 0
            print(f"✅ صفحه {page} معتبر - {movie_count} فیلم")
        else:
            consecutive_empty += 1
            print(f"❌ صفحه {page} خالی ({consecutive_empty}/3)")
        
        page += 1
        
        # محدودیت ایمنی
        if page > 500:
            break
    
    return last_valid_page

if __name__ == "__main__":
    print("🧪 تست وجود صفحات سایت myf2m.net")
    print("=" * 50)
    
    # تست صفحه 217 مستقیماً
    print("\n📍 تست صفحه 217:")
    exists_217, count_217 = test_page_exists(217)
    
    if exists_217:
        print(f"✅ صفحه 217 موجود است و {count_217} فیلم دارد")
    else:
        print("❌ صفحه 217 موجود نیست یا خالی است")
    
    # پیدا کردن آخرین صفحه
    print("\n" + "="*50)
    last_page = find_last_page()
    print(f"\n🎉 آخرین صفحه معتبر: {last_page}")
