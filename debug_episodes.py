#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from webscraper_serial_online import OnlineSeriesScraper

def debug_episodes():
    """دیباگ استخراج قسمت‌ها"""
    scraper = OnlineSeriesScraper()
    
    # تست با Nine Perfect Strangers
    url = 'https://www.myf2m.com/series/nine-perfect-strangers/'
    print(f"🔍 تست استخراج قسمت‌ها از: {url}")
    
    episodes = scraper.extract_episodes_and_links(url)
    
    if episodes:
        print(f"✅ {len(episodes)} قسمت استخراج شد")

        # بررسی ساختار اولین قسمت
        first_episode = episodes[0]
        print(f"\n📺 ساختار اولین قسمت:")
        for key, value in first_episode.items():
            if key == 'download_links':
                print(f"  {key}: {len(value)} لینک")
                if value:
                    print(f"    اولین لینک: {list(value[0].keys())}")
                    print(f"    محتوای اولین لینک: {value[0]}")
            else:
                print(f"  {key}: {value}")

        # تست ذخیره‌سازی
        print(f"\n💾 تست ذخیره‌سازی...")
        saved_count = scraper.save_episodes('test_series', episodes, check_duplicates=False)
        print(f"✅ {saved_count} قسمت ذخیره شد")
    else:
        print("❌ هیچ قسمتی استخراج نشد")

if __name__ == "__main__":
    debug_episodes()
