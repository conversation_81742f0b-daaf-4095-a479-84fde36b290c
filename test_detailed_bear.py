#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from webscraper_serial_online import OnlineSeriesScraper

def test_detailed_bear():
    """تست دقیق The Bear"""
    scraper = OnlineSeriesScraper()
    
    url = 'https://www.myf2m.net/series/the-bear-series/'
    print(f"🎬 تست دقیق The Bear از: {url}")
    
    episodes = scraper.extract_episodes_and_links(url)
    
    if episodes:
        print(f"✅ کل قسمت‌ها: {len(episodes)}")
        
        # بررسی دقیق لینک‌های دوبله
        dubbed_count = 0
        subtitled_count = 0
        
        for episode in episodes:
            season = episode['season_number']
            ep_num = episode['episode_number']
            
            dubbed_links = []
            subtitled_links = []
            
            for link in episode['download_links']:
                if link.get('is_dubbed', False):
                    dubbed_links.append(link)
                    dubbed_count += 1
                else:
                    subtitled_links.append(link)
                    subtitled_count += 1
            
            # نمایش قسمت‌هایی که هم دوبله هم زیرنویس دارند
            if dubbed_links and subtitled_links and season == 1 and ep_num <= 3:
                print(f"\n📺 فصل {season} قسمت {ep_num}:")
                print(f"   دوبله فارسی: {len(dubbed_links)} لینک")
                for i, link in enumerate(dubbed_links[:2]):
                    print(f"     {i+1}. {link['quality']} - {link['link_type']}")
                print(f"   زیرنویس فارسی: {len(subtitled_links)} لینک")
                for i, link in enumerate(subtitled_links[:2]):
                    print(f"     {i+1}. {link['quality']} - {link['link_type']}")
        
        print(f"\n📊 خلاصه:")
        print(f"   کل لینک‌های دوبله: {dubbed_count}")
        print(f"   کل لینک‌های زیرنویس: {subtitled_count}")
        
        # بررسی فصل‌ها
        seasons = {}
        for episode in episodes:
            season = episode['season_number']
            if season not in seasons:
                seasons[season] = {'episodes': 0, 'dubbed': 0, 'subtitled': 0}
            
            seasons[season]['episodes'] += 1
            
            for link in episode['download_links']:
                if link.get('is_dubbed', False):
                    seasons[season]['dubbed'] += 1
                else:
                    seasons[season]['subtitled'] += 1
        
        print(f"\n📺 آمار فصل‌ها:")
        for season in sorted(seasons.keys()):
            data = seasons[season]
            print(f"   فصل {season}: {data['episodes']} قسمت")
            print(f"     دوبله: {data['dubbed']} لینک")
            print(f"     زیرنویس: {data['subtitled']} لینک")
    
    else:
        print("❌ هیچ قسمتی استخراج نشد")

if __name__ == "__main__":
    test_detailed_bear()
