#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from webscraper_serial_online import OnlineSeriesScraper

def test_improved_extraction():
    """تست استخراج بهبود یافته"""
    scraper = OnlineSeriesScraper()
    
    # تست سریال‌های مختلف با URL های واقعی
    test_series = [
        ('The Bear', 'https://www.myf2m.net/series/the-bear-series/'),
    ]
    
    for name, url in test_series:
        print(f"\n{'='*60}")
        print(f"🎬 تست {name}")
        print('='*60)
        
        episodes = scraper.extract_episodes_and_links(url)
        
        if episodes:
            print(f"✅ کل قسمت‌ها: {len(episodes)}")
            
            # گروه‌بندی بر اساس فصل
            seasons = {}
            for episode in episodes:
                season = episode['season_number']
                if season not in seasons:
                    seasons[season] = []
                seasons[season].append(episode)
            
            print(f"📊 تعداد فصل‌ها: {len(seasons)}")
            
            # نمایش جزئیات
            for season_num in sorted(seasons.keys()):
                season_episodes = seasons[season_num]
                print(f"\n📺 فصل {season_num}: {len(season_episodes)} قسمت")
                
                # نمایش چند قسمت اول با جزئیات audio_type
                for i, episode in enumerate(season_episodes[:3]):
                    links = episode["download_links"]
                    print(f"   - قسمت {episode['episode_number']}: {len(links)} لینک")
                    
                    # نمایش انواع لینک‌ها
                    for j, link in enumerate(links[:2]):
                        audio_type = link.get('link_type', 'نامشخص')
                        quality = link.get('quality', 'نامشخص')
                        print(f"     {j+1}. {quality} - {audio_type}")
                
                if len(season_episodes) > 3:
                    print(f"   ... و {len(season_episodes) - 3} قسمت دیگر")
            
            # بررسی تنوع audio_type
            audio_types = set()
            for episode in episodes:
                for link in episode['download_links']:
                    audio_types.add(link.get('link_type', 'نامشخص'))
            
            print(f"\n🎵 انواع صدا یافت شده: {', '.join(audio_types)}")
            
        else:
            print("❌ هیچ قسمتی یافت نشد")

if __name__ == "__main__":
    test_improved_extraction()
