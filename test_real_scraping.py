#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from webscraper_serial_online import OnlineSeriesScraper
from bs4 import BeautifulSoup

def test_real_scraping():
    """تست واقعی اسکرپ صفحه اول"""
    scraper = OnlineSeriesScraper()
    scraper.check_updates = False  # غیرفعال کردن بررسی قسمت‌های جدید
    
    print("🔍 تست واقعی اسکرپ صفحه اول")
    print("="*50)
    print(f"📂 تعداد سریال‌های پردازش شده: {len(scraper.processed_series)}")
    
    # دریافت صفحه اول
    url = "https://www.myf2m.com/series/"
    response = scraper.make_request(url)
    if not response:
        print("❌ خطا در دریافت صفحه")
        return
    
    soup = BeautifulSoup(response.content, 'html.parser')
    series_list = scraper.extract_series_from_page(soup)
    
    print(f"📺 {len(series_list)} سریال از صفحه استخراج شد")
    
    # بررسی اولین 5 سریال
    for i, series_info in enumerate(series_list[:5]):
        series_name = series_info['title']
        series_url = series_info['url']
        
        print(f"\n🎬 سریال {i+1}: {series_name[:50]}...")
        print(f"   URL: {series_url}")
        
        # تولید ID
        series_id = scraper.generate_series_id(series_name, series_url)
        print(f"   ID: {series_id}")
        
        # بررسی وجود در فایل پردازش شده
        if series_id in scraper.processed_series:
            print("   ✅ در فایل پردازش شده موجود است")
            stored_info = scraper.processed_series[series_id]
            stored_name = stored_info.get('name', 'نامشخص')
            print(f"      نام ذخیره شده: {stored_name[:50]}...")
            
            # مقایسه نام‌ها
            if series_name == stored_name:
                print("      ✅ نام‌ها یکسان هستند")
            else:
                print("      ⚠️ نام‌ها متفاوت هستند!")
                print(f"         فعلی: {series_name}")
                print(f"         ذخیره شده: {stored_name}")
            
            # شبیه‌سازی process_single_series
            print("   🔄 شبیه‌سازی process_single_series:")
            if hasattr(scraper, 'check_updates') and scraper.check_updates:
                print("      بررسی قسمت‌های جدید فعال است")
            else:
                print("      ⏭️ سریال قبلاً پردازش شده - باید رد شود")
        else:
            print("   ❌ در فایل پردازش شده موجود نیست - باید پردازش شود")

if __name__ == "__main__":
    test_real_scraping()
