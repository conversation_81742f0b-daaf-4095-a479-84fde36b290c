#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from webscraper_serial_online import OnlineSeriesScraper
from bs4 import BeautifulSoup
import re

def debug_bear_page():
    scraper = OnlineSeriesScraper()
    response = scraper.make_request('https://www.myf2m.com/series/the-bear-season-4/')

    if response:
        # جستجو در کل محتوای HTML
        html_content = response.text
        
        # جستجوی الگوهای مختلف لینک دانلود
        patterns = [
            r'href=["\']([^"\']*(?:mkv|mp4|avi)[^"\']*)["\']',
            r'(https?://[^\s"\']*(?:mkv|mp4|avi)[^\s"\']*)',
            r'data-[^=]*=["\']([^"\']*(?:mkv|mp4|avi)[^"\']*)["\']'
        ]
        
        all_video_links = []
        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    link = match[0]
                else:
                    link = match
                if link not in all_video_links:
                    all_video_links.append(link)
        
        print(f'لینک‌های ویدیو یافت شده: {len(all_video_links)}')
        for i, link in enumerate(all_video_links[:5]):
            print(f'{i+1}. {link[:100]}...')
        
        # جستجوی باکس‌های حاوی اطلاعات قسمت
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # جستجوی div هایی که ممکن است حاوی اطلاعات قسمت باشند
        content_divs = soup.find_all(['div', 'section', 'article'])
        episode_divs = []
        
        for div in content_divs:
            text = div.get_text()
            if 'قسمت' in text and ('فصل' in text or 'دانلود' in text):
                episode_divs.append(div)
        
        print(f'\nباکس‌های حاوی اطلاعات قسمت: {len(episode_divs)}')
        for i, div in enumerate(episode_divs[:3]):
            text = div.get_text()[:100].replace('\n', ' ')
            print(f'{i+1}. {text}...')
            
            # جستجوی لینک در این div
            links_in_div = div.find_all('a', href=True)
            print(f'   لینک‌ها در این div: {len(links_in_div)}')
            for j, link in enumerate(links_in_div[:2]):
                print(f'     {j+1}. {link.get_text()[:30]}... -> {link.get("href")[:50]}...')

if __name__ == "__main__":
    debug_bear_page()
