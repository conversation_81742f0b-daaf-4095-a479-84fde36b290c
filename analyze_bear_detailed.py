#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from webscraper_serial_online import OnlineSeriesScraper
from bs4 import BeautifulSoup
import re

def analyze_bear_detailed():
    """تحلیل دقیق The Bear"""
    scraper = OnlineSeriesScraper()
    
    # URL واقعی The Bear
    url = 'https://www.myf2m.net/series/the-bear-series/'
    print(f"🎬 تحلیل دقیق The Bear از: {url}")
    
    response = scraper.make_request(url)
    if not response:
        print("❌ خطا در دریافت صفحه")
        return
        
    soup = BeautifulSoup(response.content, 'html.parser')
    
    print("\n🔍 1. جستجوی لینک‌های مستقیم ویدیو:")
    video_links = soup.find_all('a', href=lambda x: x and any(ext in x for ext in ['.mkv', '.mp4', '.avi']))
    print(f"   تعداد: {len(video_links)}")
    
    # تحلیل لینک‌های ویدیو
    seasons_found = {}
    for i, link in enumerate(video_links[:20]):  # فقط 20 تای اول
        href = link.get('href', '')
        text = link.get_text().strip()
        
        # استخراج فصل و قسمت از URL
        season_from_url = None
        episode_from_url = None
        
        # الگوهای مختلف در URL
        url_patterns = [
            r'S(\d+)E(\d+)',
            r'S(\d+)/.*E(\d+)',
            r'Season[._-](\d+).*Episode[._-](\d+)',
            r'/S(\d+)/',
        ]
        
        for pattern in url_patterns:
            match = re.search(pattern, href, re.IGNORECASE)
            if match:
                if len(match.groups()) >= 2:
                    season_from_url = int(match.group(1))
                    episode_from_url = int(match.group(2))
                elif len(match.groups()) == 1:
                    season_from_url = int(match.group(1))
                break
        
        # استخراج فصل و قسمت از متن
        season_from_text, episode_from_text = scraper.extract_season_and_episode(text)
        
        # تشخیص دوبله/زیرنویس
        is_dubbed = scraper.is_dubbed_version(text + ' ' + href)
        audio_type = 'دوبله فارسی' if is_dubbed else 'زیرنویس فارسی'
        
        season = season_from_url or season_from_text
        episode = episode_from_url or episode_from_text
        
        if season not in seasons_found:
            seasons_found[season] = {'episodes': set(), 'dubbed': set(), 'subtitled': set()}
        
        seasons_found[season]['episodes'].add(episode)
        if is_dubbed:
            seasons_found[season]['dubbed'].add(episode)
        else:
            seasons_found[season]['subtitled'].add(episode)
        
        if i < 5:  # نمایش 5 نمونه اول
            print(f"   {i+1}. فصل {season}, قسمت {episode} - {audio_type}")
            print(f"      متن: {text[:50]}...")
            print(f"      URL: {href[:80]}...")
    
    print(f"\n📊 خلاصه فصل‌ها:")
    for season in sorted(seasons_found.keys()):
        data = seasons_found[season]
        total_episodes = len(data['episodes'])
        dubbed_count = len(data['dubbed'])
        subtitled_count = len(data['subtitled'])
        
        print(f"   فصل {season}:")
        print(f"     کل قسمت‌ها: {total_episodes}")
        print(f"     دوبله فارسی: {dubbed_count} قسمت")
        print(f"     زیرنویس فارسی: {subtitled_count} قسمت")
        print(f"     قسمت‌ها: {sorted(data['episodes'])}")
    
    print(f"\n🔍 2. جستجوی باکس‌های دانلود:")
    download_boxes = soup.find_all(['div'], class_=lambda x: x and 
                                 any(cls in ' '.join(x) if isinstance(x, list) else cls in x 
                                     for cls in ['download-list', 'download-season']))
    print(f"   تعداد: {len(download_boxes)}")
    
    for i, box in enumerate(download_boxes[:3]):
        box_classes = box.get('class', [])
        box_class_str = ' '.join(box_classes) if box_classes else ''
        print(f"   باکس {i+1}: کلاس‌ها = {box_class_str}")
        
        # تشخیص نوع از کلاس
        if 'dubbled' in box_class_str:
            audio_type = 'دوبله فارسی'
        elif 'hardsub' in box_class_str:
            audio_type = 'زیرنویس فارسی'
        else:
            audio_type = 'نامشخص'
        
        print(f"           نوع صدا: {audio_type}")
        
        # لینک‌های داخل باکس
        links = box.find_all('a', href=True)
        video_links_in_box = [link for link in links if 
                             any(ext in link.get('href', '') for ext in ['.mkv', '.mp4', '.avi'])]
        print(f"           لینک‌های ویدیو: {len(video_links_in_box)}")

if __name__ == "__main__":
    analyze_bear_detailed()
