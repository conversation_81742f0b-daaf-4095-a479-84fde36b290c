!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).bootstrap=t()}(this,function(){"use strict";let e="transitionend",t=e=>(e&&window.CSS&&window.CSS.escape&&(e=e.replace(/#([^\s"#']+)/g,(e,t)=>`#${CSS.escape(t)}`)),e),i=e=>null==e?`${e}`:Object.prototype.toString.call(e).match(/\s([a-z]+)/i)[1].toLowerCase(),s=e=>{if(!e)return 0;let{transitionDuration:t,transitionDelay:i}=window.getComputedStyle(e),s=Number.parseFloat(t),n=Number.parseFloat(i);return s||n?(t=t.split(",")[0],i=i.split(",")[0],(Number.parseFloat(t)+Number.parseFloat(i))*1e3):0},n=t=>{t.dispatchEvent(new Event(e))},r=e=>!!e&&"object"==typeof e&&(void 0!==e.jquery&&(e=e[0]),void 0!==e.nodeType),l=e=>r(e)?e.jquery?e[0]:e:"string"==typeof e&&e.length>0?document.querySelector(t(e)):null,o=e=>{if(!r(e)||0===e.getClientRects().length)return!1;let t="visible"===getComputedStyle(e).getPropertyValue("visibility"),i=e.closest("details:not([open])");if(!i)return t;if(i!==e){let s=e.closest("summary");if(s&&s.parentNode!==i||null===s)return!1}return t},a=e=>!!(!e||e.nodeType!==Node.ELEMENT_NODE||e.classList.contains("disabled"))||(void 0!==e.disabled?e.disabled:e.hasAttribute("disabled")&&"false"!==e.getAttribute("disabled")),h=e=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof e.getRootNode){let t=e.getRootNode();return t instanceof ShadowRoot?t:null}return e instanceof ShadowRoot?e:e.parentNode?h(e.parentNode):null},c=e=>{e.offsetHeight},d=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,u=[],f=e=>{"loading"===document.readyState?(u.length||document.addEventListener("DOMContentLoaded",()=>{for(let e of u)e()}),u.push(e)):e()},g=()=>"rtl"===document.documentElement.dir,m=e=>{f(()=>{let t=d();if(t){let i=e.NAME,s=t.fn[i];t.fn[i]=e.jQueryInterface,t.fn[i].Constructor=e,t.fn[i].noConflict=()=>(t.fn[i]=s,e.jQueryInterface)}})},p=(e,t=[],i=e)=>"function"==typeof e?e(...t):i,b=(t,i,r=!0)=>{if(!r){p(t);return}let l=s(i)+5,o=!1,a=({target:s})=>{s===i&&(o=!0,i.removeEventListener(e,a),p(t))};i.addEventListener(e,a),setTimeout(()=>{o||n(i)},l)},v=/[^.]*(?=\..*)\.|.*/,y=/\..*/,A=/::\d+$/,w={},E=1,C={mouseenter:"mouseover",mouseleave:"mouseout"},k=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function T(e,t){return t&&`${t}::${E++}`||e.uidEvent||E++}function S(e){let t=T(e);return e.uidEvent=t,w[t]=w[t]||{},w[t]}function $(e,t,i=null){return Object.values(e).find(e=>e.callable===t&&e.delegationSelector===i)}function L(e,t,i){let s="string"==typeof t,n=M(e);return k.has(n)||(n=e),[s,s?i:t||i,n]}function D(e,t,i,s,n){var r,l,o,a,h,c;if("string"!=typeof t||!e)return;let[d,u,f]=L(t,i,s);t in C&&(u=(r=u,function(e){if(!e.relatedTarget||e.relatedTarget!==e.delegateTarget&&!e.delegateTarget.contains(e.relatedTarget))return r.call(this,e)}));let g=S(e),m=g[f]||(g[f]={}),p=$(m,u,d?i:null);if(p){p.oneOff=p.oneOff&&n;return}let b=T(u,t.replace(v,"")),y=d?(l=e,o=i,a=u,function e(t){let i=l.querySelectorAll(o);for(let{target:s}=t;s&&s!==this;s=s.parentNode)for(let n of i)if(n===s)return _(t,{delegateTarget:s}),e.oneOff&&x.off(l,t.type,o,a),a.apply(s,[t])}):(h=e,c=u,function e(t){return _(t,{delegateTarget:h}),e.oneOff&&x.off(h,t.type,c),c.apply(h,[t])});y.delegationSelector=d?i:null,y.callable=u,y.oneOff=n,y.uidEvent=b,m[b]=y,e.addEventListener(f,y,d)}function N(e,t,i,s,n){let r=$(t[i],s,n);r&&(e.removeEventListener(i,r,Boolean(n)),delete t[i][r.uidEvent])}function O(e,t,i,s){let n=t[i]||{};for(let[r,l]of Object.entries(n))r.includes(s)&&N(e,t,i,l.callable,l.delegationSelector)}function M(e){return C[e=e.replace(y,"")]||e}let x={on(e,t,i,s){D(e,t,i,s,!1)},one(e,t,i,s){D(e,t,i,s,!0)},off(e,t,i,s){if("string"!=typeof t||!e)return;let[n,r,l]=L(t,i,s),o=l!==t,a=S(e),h=a[l]||{},c=t.startsWith(".");if(void 0!==r){if(!Object.keys(h).length)return;N(e,a,l,r,n?i:null);return}if(c)for(let d of Object.keys(a))O(e,a,d,t.slice(1));for(let[u,f]of Object.entries(h)){let g=u.replace(A,"");(!o||t.includes(g))&&N(e,a,l,f.callable,f.delegationSelector)}},trigger(e,t,i){if("string"!=typeof t||!e)return null;let s=d(),n=M(t),r=null,l=!0,o=!0,a=!1;t!==n&&s&&(r=s.Event(t,i),s(e).trigger(r),l=!r.isPropagationStopped(),o=!r.isImmediatePropagationStopped(),a=r.isDefaultPrevented());let h=new Event(t,{bubbles:l,cancelable:!0});return h=_(h,i),a&&h.preventDefault(),o&&e.dispatchEvent(h),h.defaultPrevented&&r&&r.preventDefault(),h}};function _(e,t={}){for(let[i,s]of Object.entries(t))try{e[i]=s}catch(n){Object.defineProperty(e,i,{configurable:!0,get:()=>s})}return e}let F=new Map,I={set(e,t,i){F.has(e)||F.set(e,new Map);let s=F.get(e);if(!s.has(t)&&0!==s.size){console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(s.keys())[0]}.`);return}s.set(t,i)},get:(e,t)=>F.has(e)&&F.get(e).get(t)||null,remove(e,t){if(!F.has(e))return;let i=F.get(e);i.delete(t),0===i.size&&F.delete(e)}};function j(e){if("true"===e)return!0;if("false"===e)return!1;if(e===Number(e).toString())return Number(e);if(""===e||"null"===e)return null;if("string"!=typeof e)return e;try{return JSON.parse(decodeURIComponent(e))}catch(t){return e}}function P(e){return e.replace(/[A-Z]/g,e=>`-${e.toLowerCase()}`)}let z={setDataAttribute(e,t,i){e.setAttribute(`data-bs-${P(t)}`,i)},removeDataAttribute(e,t){e.removeAttribute(`data-bs-${P(t)}`)},getDataAttributes(e){if(!e)return{};let t={},i=Object.keys(e.dataset).filter(e=>e.startsWith("bs")&&!e.startsWith("bsConfig"));for(let s of i){let n=s.replace(/^bs/,"");t[n=n.charAt(0).toLowerCase()+n.slice(1,n.length)]=j(e.dataset[s])}return t},getDataAttribute:(e,t)=>j(e.getAttribute(`data-bs-${P(t)}`))};class q{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw Error('You have to implement the static method "NAME", for each component!')}_getConfig(e){return e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e}_mergeConfigObj(e,t){let i=r(t)?z.getDataAttribute(t,"config"):{};return{...this.constructor.Default,..."object"==typeof i?i:{},...r(t)?z.getDataAttributes(t):{},..."object"==typeof e?e:{}}}_typeCheckConfig(e,t=this.constructor.DefaultType){for(let[s,n]of Object.entries(t)){let l=e[s],o=r(l)?"element":i(l);if(!RegExp(n).test(o))throw TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${s}" provided type "${o}" but expected type "${n}".`)}}}class W extends q{constructor(e,t){if(super(),!(e=l(e)))return;this._element=e,this._config=this._getConfig(t),I.set(this._element,this.constructor.DATA_KEY,this)}dispose(){for(let e of(I.remove(this._element,this.constructor.DATA_KEY),x.off(this._element,this.constructor.EVENT_KEY),Object.getOwnPropertyNames(this)))this[e]=null}_queueCallback(e,t,i=!0){b(e,t,i)}_getConfig(e){return e=this._mergeConfigObj(e,this._element),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}static getInstance(e){return I.get(l(e),this.DATA_KEY)}static getOrCreateInstance(e,t={}){return this.getInstance(e)||new this(e,"object"==typeof t?t:null)}static get VERSION(){return"5.3.0-alpha1"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(e){return`${e}${this.EVENT_KEY}`}}let V=e=>{let i=e.getAttribute("data-bs-target");if(!i||"#"===i){let s=e.getAttribute("href");if(!s||!s.includes("#")&&!s.startsWith("."))return null;s.includes("#")&&!s.startsWith("#")&&(s=`#${s.split("#")[1]}`),i=s&&"#"!==s?s.trim():null}return t(i)},B={find:(e,t=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(t,e)),findOne:(e,t=document.documentElement)=>Element.prototype.querySelector.call(t,e),children:(e,t)=>[].concat(...e.children).filter(e=>e.matches(t)),parents(e,t){let i=[],s=e.parentNode.closest(t);for(;s;)i.push(s),s=s.parentNode.closest(t);return i},prev(e,t){let i=e.previousElementSibling;for(;i;){if(i.matches(t))return[i];i=i.previousElementSibling}return[]},next(e,t){let i=e.nextElementSibling;for(;i;){if(i.matches(t))return[i];i=i.nextElementSibling}return[]},focusableChildren(e){let t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(e=>`${e}:not([tabindex^="-"])`).join(",");return this.find(t,e).filter(e=>!a(e)&&o(e))},getSelectorFromElement(e){let t=V(e);return t&&B.findOne(t)?t:null},getElementFromSelector(e){let t=V(e);return t?B.findOne(t):null},getMultipleElementsFromSelector(e){let t=V(e);return t?B.find(t):[]}},Y=(e,t="hide")=>{let i=`click.dismiss${e.EVENT_KEY}`,s=e.NAME;x.on(document,i,`[data-bs-dismiss="${s}"]`,function(i){if(["A","AREA"].includes(this.tagName)&&i.preventDefault(),a(this))return;let n=B.getElementFromSelector(this)||this.closest(`.${s}`),r=e.getOrCreateInstance(n);r[t]()})},K=".bs.alert",R=`close${K}`,H=`closed${K}`;class Q extends W{static get NAME(){return"alert"}close(){let e=x.trigger(this._element,R);if(e.defaultPrevented)return;this._element.classList.remove("show");let t=this._element.classList.contains("fade");this._queueCallback(()=>this._destroyElement(),this._element,t)}_destroyElement(){this._element.remove(),x.trigger(this._element,H),this.dispose()}static jQueryInterface(e){return this.each(function(){let t=Q.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError(`No method named "${e}"`);t[e](this)}})}}Y(Q,"close"),m(Q);let U=".bs.swipe",Z=".bs.collapse",G=`show${Z}`,J=`shown${Z}`,X=`hide${Z}`,ee=`hidden${Z}`,et=`click${Z}.data-api`,ei="show",es="collapse",en="collapsing",er=`:scope .${es} .${es}`,el='[data-bs-toggle="collapse"]',eo={parent:null,toggle:!0},ea={parent:"(null|element)",toggle:"boolean"};class eh extends W{constructor(e,t){super(e,t),this._isTransitioning=!1,this._triggerArray=[];let i=B.find(el);for(let s of i){let n=B.getSelectorFromElement(s),r=B.find(n).filter(e=>e===this._element);null!==n&&r.length&&this._triggerArray.push(s)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return eo}static get DefaultType(){return ea}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let e=[];if(this._config.parent&&(e=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter(e=>e!==this._element).map(e=>eh.getOrCreateInstance(e,{toggle:!1}))),e.length&&e[0]._isTransitioning)return;let t=x.trigger(this._element,G);if(t.defaultPrevented)return;for(let i of e)i.hide();let s=this._getDimension();this._element.classList.remove(es),this._element.classList.add(en),this._element.style[s]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;let n=()=>{this._isTransitioning=!1,this._element.classList.remove(en),this._element.classList.add(es,ei),this._element.style[s]="",x.trigger(this._element,J)},r=s[0].toUpperCase()+s.slice(1),l=`scroll${r}`;this._queueCallback(n,this._element,!0),this._element.style[s]=`${this._element[l]}px`}hide(){if(this._isTransitioning||!this._isShown())return;let e=x.trigger(this._element,X);if(e.defaultPrevented)return;let t=this._getDimension();for(let i of(this._element.style[t]=`${this._element.getBoundingClientRect()[t]}px`,c(this._element),this._element.classList.add(en),this._element.classList.remove(es,ei),this._triggerArray)){let s=B.getElementFromSelector(i);s&&!this._isShown(s)&&this._addAriaAndCollapsedClass([i],!1)}this._isTransitioning=!0;let n=()=>{this._isTransitioning=!1,this._element.classList.remove(en),this._element.classList.add(es),x.trigger(this._element,ee)};this._element.style[t]="",this._queueCallback(n,this._element,!0)}_isShown(e=this._element){return e.classList.contains(ei)}_configAfterMerge(e){return e.toggle=Boolean(e.toggle),e.parent=l(e.parent),e}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(!this._config.parent)return;let e=this._getFirstLevelChildren(el);for(let t of e){let i=B.getElementFromSelector(t);i&&this._addAriaAndCollapsedClass([t],this._isShown(i))}}_getFirstLevelChildren(e){let t=B.find(er,this._config.parent);return B.find(e,this._config.parent).filter(e=>!t.includes(e))}_addAriaAndCollapsedClass(e,t){if(e.length)for(let i of e)i.classList.toggle("collapsed",!t),i.setAttribute("aria-expanded",t)}static jQueryInterface(e){let t={};return"string"==typeof e&&/show|hide/.test(e)&&(t.toggle=!1),this.each(function(){let i=eh.getOrCreateInstance(this,t);if("string"==typeof e){if(void 0===i[e])throw TypeError(`No method named "${e}"`);i[e]()}})}}x.on(document,et,el,function(e){for(let t of(("A"===e.target.tagName||e.delegateTarget&&"A"===e.delegateTarget.tagName)&&e.preventDefault(),B.getMultipleElementsFromSelector(this)))eh.getOrCreateInstance(t,{toggle:!1}).toggle()}),m(eh);let ec=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",ed=".sticky-top",eu="padding-right",ef="margin-right";class eg{constructor(){this._element=document.body}getWidth(){let e=document.documentElement.clientWidth;return Math.abs(window.innerWidth-e)}hide(){let e=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,eu,t=>t+e),this._setElementAttributes(ec,eu,t=>t+e),this._setElementAttributes(ed,ef,t=>t-e)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,eu),this._resetElementAttributes(ec,eu),this._resetElementAttributes(ed,ef)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(e,t,i){let s=this.getWidth(),n=e=>{if(e!==this._element&&window.innerWidth>e.clientWidth+s)return;this._saveInitialAttribute(e,t);let n=window.getComputedStyle(e).getPropertyValue(t);e.style.setProperty(t,`${i(Number.parseFloat(n))}px`)};this._applyManipulationCallback(e,n)}_saveInitialAttribute(e,t){let i=e.style.getPropertyValue(t);i&&z.setDataAttribute(e,t,i)}_resetElementAttributes(e,t){let i=e=>{let i=z.getDataAttribute(e,t);if(null===i){e.style.removeProperty(t);return}z.removeDataAttribute(e,t),e.style.setProperty(t,i)};this._applyManipulationCallback(e,i)}_applyManipulationCallback(e,t){if(r(e)){t(e);return}for(let i of B.find(e,this._element))t(i)}}let e8="backdrop",em="show",ep=`mousedown.bs.${e8}`,eb={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},ev={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class ey extends q{constructor(e){super(),this._config=this._getConfig(e),this._isAppended=!1,this._element=null}static get Default(){return eb}static get DefaultType(){return ev}static get NAME(){return e8}show(e){if(!this._config.isVisible){p(e);return}this._append();let t=this._getElement();this._config.isAnimated&&c(t),t.classList.add(em),this._emulateAnimation(()=>{p(e)})}hide(e){if(!this._config.isVisible){p(e);return}this._getElement().classList.remove(em),this._emulateAnimation(()=>{this.dispose(),p(e)})}dispose(){this._isAppended&&(x.off(this._element,ep),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){let e=document.createElement("div");e.className=this._config.className,this._config.isAnimated&&e.classList.add("fade"),this._element=e}return this._element}_configAfterMerge(e){return e.rootElement=l(e.rootElement),e}_append(){if(this._isAppended)return;let e=this._getElement();this._config.rootElement.append(e),x.on(e,ep,()=>{p(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(e){b(e,this._getElement(),this._config.isAnimated)}}let eA=".bs.focustrap",ew=`focusin${eA}`,eE=`keydown.tab${eA}`,eC="backward",ek={autofocus:!0,trapElement:null},eT={autofocus:"boolean",trapElement:"element"};class eS extends q{constructor(e){super(),this._config=this._getConfig(e),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return ek}static get DefaultType(){return eT}static get NAME(){return"focustrap"}activate(){!this._isActive&&(this._config.autofocus&&this._config.trapElement.focus(),x.off(document,eA),x.on(document,ew,e=>this._handleFocusin(e)),x.on(document,eE,e=>this._handleKeydown(e)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,x.off(document,eA))}_handleFocusin(e){let{trapElement:t}=this._config;if(e.target===document||e.target===t||t.contains(e.target))return;let i=B.focusableChildren(t);0===i.length?t.focus():this._lastTabNavDirection===eC?i[i.length-1].focus():i[0].focus()}_handleKeydown(e){"Tab"===e.key&&(this._lastTabNavDirection=e.shiftKey?eC:"forward")}}let e$=".bs.modal",e9=`hide${e$}`,eL=`hidePrevented${e$}`,eD=`hidden${e$}`,eN=`show${e$}`,eO=`shown${e$}`,eM=`resize${e$}`,ex=`click.dismiss${e$}`,e_=`mousedown.dismiss${e$}`,eF=`keydown.dismiss${e$}`,eI=`click${e$}.data-api`,ej="modal-open",eP="show",ez="modal-static",eq={backdrop:!0,focus:!0,keyboard:!0},eW={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class eV extends W{constructor(e,t){super(e,t),this._dialog=B.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new eg,this._addEventListeners()}static get Default(){return eq}static get DefaultType(){return eW}static get NAME(){return"modal"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){if(this._isShown||this._isTransitioning)return;let t=x.trigger(this._element,eN,{relatedTarget:e});!t.defaultPrevented&&(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(ej),this._adjustDialog(),this._backdrop.show(()=>this._showElement(e)))}hide(){if(!this._isShown||this._isTransitioning)return;let e=x.trigger(this._element,e9);!e.defaultPrevented&&(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(eP),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated()))}dispose(){for(let e of[window,this._dialog])x.off(e,e$);this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new ey({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new eS({trapElement:this._element})}_showElement(e){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;let t=B.findOne(".modal-body",this._dialog);t&&(t.scrollTop=0),c(this._element),this._element.classList.add(eP);let i=()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,x.trigger(this._element,eO,{relatedTarget:e})};this._queueCallback(i,this._dialog,this._isAnimated())}_addEventListeners(){x.on(this._element,eF,e=>{if("Escape"===e.key){if(this._config.keyboard){e.preventDefault(),this.hide();return}this._triggerBackdropTransition()}}),x.on(window,eM,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),x.on(this._element,e_,e=>{x.one(this._element,ex,t=>{if(this._element===e.target&&this._element===t.target){if("static"===this._config.backdrop){this._triggerBackdropTransition();return}this._config.backdrop&&this.hide()}})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(ej),this._resetAdjustments(),this._scrollBar.reset(),x.trigger(this._element,eD)})}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){let e=x.trigger(this._element,eL);if(e.defaultPrevented)return;let t=this._element.scrollHeight>document.documentElement.clientHeight,i=this._element.style.overflowY;!("hidden"===i||this._element.classList.contains(ez))&&(t||(this._element.style.overflowY="hidden"),this._element.classList.add(ez),this._queueCallback(()=>{this._element.classList.remove(ez),this._queueCallback(()=>{this._element.style.overflowY=i},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){let e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._scrollBar.getWidth(),i=t>0;if(i&&!e){let s=g()?"paddingLeft":"paddingRight";this._element.style[s]=`${t}px`}if(!i&&e){let n=g()?"paddingRight":"paddingLeft";this._element.style[n]=`${t}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(e,t){return this.each(function(){let i=eV.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===i[e])throw TypeError(`No method named "${e}"`);i[e](t)}})}}x.on(document,eI,'[data-bs-toggle="modal"]',function(e){let t=B.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&e.preventDefault(),x.one(t,eN,e=>{!e.defaultPrevented&&x.one(t,eD,()=>{o(this)&&this.focus()})});let i=B.findOne(".modal.show");i&&eV.getInstance(i).hide();let s=eV.getOrCreateInstance(t);s.toggle(this)}),Y(eV),m(eV);let eB=".bs.offcanvas",eY=".data-api",eK=`load${eB}${eY}`,eR="show",eH="showing",eQ="hiding",e3=".offcanvas.show",e1=`show${eB}`,eU=`shown${eB}`,e2=`hide${eB}`,e0=`hidePrevented${eB}`,e4=`hidden${eB}`,eZ=`resize${eB}`,eG=`click${eB}${eY}`,eJ=`keydown.dismiss${eB}`,eX={backdrop:!0,keyboard:!0,scroll:!1},e5={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class e6 extends W{constructor(e,t){super(e,t),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return eX}static get DefaultType(){return e5}static get NAME(){return"offcanvas"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){if(this._isShown)return;let t=x.trigger(this._element,e1,{relatedTarget:e});if(t.defaultPrevented)return;this._isShown=!0,this._backdrop.show(),this._config.scroll||new eg().hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(eH);let i=()=>{(!this._config.scroll||this._config.backdrop)&&this._focustrap.activate(),this._element.classList.add(eR),this._element.classList.remove(eH),x.trigger(this._element,eU,{relatedTarget:e})};this._queueCallback(i,this._element,!0)}hide(){if(!this._isShown)return;let e=x.trigger(this._element,e2);if(e.defaultPrevented)return;this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(eQ),this._backdrop.hide();let t=()=>{this._element.classList.remove(eR,eQ),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||new eg().reset(),x.trigger(this._element,e4)};this._queueCallback(t,this._element,!0)}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){let e=()=>{if("static"===this._config.backdrop){x.trigger(this._element,e0);return}this.hide()},t=Boolean(this._config.backdrop);return new ey({className:"offcanvas-backdrop",isVisible:t,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:t?e:null})}_initializeFocusTrap(){return new eS({trapElement:this._element})}_addEventListeners(){x.on(this._element,eJ,e=>{if("Escape"===e.key){if(!this._config.keyboard){x.trigger(this._element,e0);return}this.hide()}})}static jQueryInterface(e){return this.each(function(){let t=e6.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError(`No method named "${e}"`);t[e](this)}})}}x.on(document,eG,'[data-bs-toggle="offcanvas"]',function(e){let t=B.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&e.preventDefault(),a(this))return;x.one(t,e4,()=>{o(this)&&this.focus()});let i=B.findOne(e3);i&&i!==t&&e6.getInstance(i).hide();let s=e6.getOrCreateInstance(t);s.toggle(this)}),x.on(window,eK,()=>{for(let e of B.find(e3))e6.getOrCreateInstance(e).show()}),x.on(window,eZ,()=>{for(let e of B.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(e).position&&e6.getOrCreateInstance(e).hide()}),Y(e6),m(e6);let e7=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),te=/^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i,tt=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i,ti=(e,t)=>{let i=e.nodeName.toLowerCase();return t.includes(i)?!e7.has(i)||Boolean(te.test(e.nodeValue)||tt.test(e.nodeValue)):t.filter(e=>e instanceof RegExp).some(e=>e.test(i))},ts={allowList:{"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},tn={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},tr={entry:"(string|element|function|null)",selector:"(string|element)"};class tl extends q{constructor(e){super(),this._config=this._getConfig(e)}static get Default(){return ts}static get DefaultType(){return tn}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map(e=>this._resolvePossibleFunction(e)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(e){return this._checkContent(e),this._config.content={...this._config.content,...e},this}toHtml(){let e=document.createElement("div");for(let[t,i]of(e.innerHTML=this._maybeSanitize(this._config.template),Object.entries(this._config.content)))this._setContent(e,i,t);let s=e.children[0],n=this._resolvePossibleFunction(this._config.extraClass);return n&&s.classList.add(...n.split(" ")),s}_typeCheckConfig(e){super._typeCheckConfig(e),this._checkContent(e.content)}_checkContent(e){for(let[t,i]of Object.entries(e))super._typeCheckConfig({selector:t,entry:i},tr)}_setContent(e,t,i){let s=B.findOne(i,e);if(s){if(!(t=this._resolvePossibleFunction(t))){s.remove();return}if(r(t)){this._putElementInTemplate(l(t),s);return}if(this._config.html){s.innerHTML=this._maybeSanitize(t);return}s.textContent=t}}_maybeSanitize(e){return this._config.sanitize?function e(t,i,s){if(!t.length)return t;if(s&&"function"==typeof s)return s(t);let n=new window.DOMParser,r=n.parseFromString(t,"text/html"),l=[].concat(...r.body.querySelectorAll("*"));for(let o of l){let a=o.nodeName.toLowerCase();if(!Object.keys(i).includes(a)){o.remove();continue}let h=[].concat(...o.attributes),c=[].concat(i["*"]||[],i[a]||[]);for(let d of h)ti(d,c)||o.removeAttribute(d.nodeName)}return r.body.innerHTML}(e,this._config.allowList,this._config.sanitizeFn):e}_resolvePossibleFunction(e){return p(e,[this])}_putElementInTemplate(e,t){if(this._config.html){t.innerHTML="",t.append(e);return}t.textContent=e.textContent}}let to=".bs.scrollspy",ta=`activate${to}`,th=`click${to}`,tc=`load${to}.data-api`,td="active",tu="[href]",tf=".nav-link",tg=`${tf}, .nav-item > ${tf}, .list-group-item`,t8={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},tm={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class tp extends W{constructor(e,t){super(e,t),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return t8}static get DefaultType(){return tm}static get NAME(){return"scrollspy"}refresh(){for(let e of(this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver(),this._observableSections.values()))this._observer.observe(e)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(e){return e.target=l(e.target)||document.body,e.rootMargin=e.offset?`${e.offset}px 0px -30%`:e.rootMargin,"string"==typeof e.threshold&&(e.threshold=e.threshold.split(",").map(e=>Number.parseFloat(e))),e}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(x.off(this._config.target,th),x.on(this._config.target,th,tu,e=>{let t=this._observableSections.get(e.target.hash);if(t){e.preventDefault();let i=this._rootElement||window,s=t.offsetTop-this._element.offsetTop;if(i.scrollTo){i.scrollTo({top:s,behavior:"smooth"});return}i.scrollTop=s}}))}_getNewObserver(){let e={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(e=>this._observerCallback(e),e)}_observerCallback(e){let t=e=>this._targetLinks.get(`#${e.target.id}`),i=e=>{this._previousScrollData.visibleEntryTop=e.target.offsetTop,this._process(t(e))},s=(this._rootElement||document.documentElement).scrollTop,n=s>=this._previousScrollData.parentScrollTop;for(let r of(this._previousScrollData.parentScrollTop=s,e)){if(!r.isIntersecting){this._activeTarget=null,this._clearActiveClass(t(r));continue}let l=r.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(n&&l){if(i(r),!s)return;continue}n||l||i(r)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;let e=B.find(tu,this._config.target);for(let t of e){if(!t.hash||a(t))continue;let i=B.findOne(t.hash,this._element);o(i)&&(this._targetLinks.set(t.hash,t),this._observableSections.set(t.hash,i))}}_process(e){this._activeTarget!==e&&(this._clearActiveClass(this._config.target),this._activeTarget=e,e.classList.add(td),this._activateParents(e),x.trigger(this._element,ta,{relatedTarget:e}))}_activateParents(e){if(e.classList.contains("dropdown-item")){B.findOne(".dropdown-toggle",e.closest(".dropdown")).classList.add(td);return}for(let t of B.parents(e,".nav, .list-group"))for(let i of B.prev(t,tg))i.classList.add(td)}_clearActiveClass(e){e.classList.remove(td);let t=B.find(`${tu}.${td}`,e);for(let i of t)i.classList.remove(td)}static jQueryInterface(e){return this.each(function(){let t=tp.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError(`No method named "${e}"`);t[e]()}})}}return x.on(window,tc,()=>{for(let e of B.find('[data-bs-spy="scroll"]'))tp.getOrCreateInstance(e)}),m(tp),{Alert:Q,Collapse:eh,Modal:eV,Offcanvas:e6,ScrollSpy:tp}});