import csv
import os
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import json

class MovieExtractor:
    def __init__(self, base_path='www.myf2m.com'):
        self.base_path = base_path
        self.movies_data = []
    
    def extract_year_from_title(self, title):
        """استخراج سال از عنوان فیلم"""
        year_match = re.search(r'(\d{4})', title)
        return year_match.group(1) if year_match else ''
    
    def extract_movie_info(self, html_file_path):
        """استخراج اطلاعات کامل فیلم از فایل HTML"""
        try:
            with open(html_file_path, 'r', encoding='utf-8') as f:
                soup = BeautifulSoup(f.read(), 'html.parser')
            
            # استخراج نام فیلم
            title_tag = soup.find('title')
            movie_title = title_tag.get_text().strip() if title_tag else ''
            
            # استخراج سال انتشار
            release_year = self.extract_year_from_title(movie_title)
            
            # استخراج ژانر
            genre = self.extract_genre(soup)
            
            # استخراج امتیاز
            rating = self.extract_rating(soup)
            
            # استخراج خلاصه داستان
            summary = self.extract_summary(soup)
            
            # استخراج پوستر
            poster_url = self.extract_poster(soup)
            
            return {
                'نام_فیلم': movie_title,
                'سال_انتشار': release_year,
                'ژانر': genre,
                'امتیاز': rating,
                'خلاصه_داستان': summary,
                'پوستر': poster_url
            }
            
        except Exception as e:
            print(f"خطا در پردازش {html_file_path}: {e}")
            return None
    
    def extract_genre(self, soup):
        """استخراج ژانر فیلم"""
        # جستجو در متاتگ‌ها
        genre_meta = soup.find('meta', {'name': 'keywords'})
        if genre_meta:
            return genre_meta.get('content', '')
        
        # جستجو در کلاس‌های مخصوص ژانر
        genre_elements = soup.find_all(class_=re.compile(r'genre|category'))
        if genre_elements:
            return ', '.join([elem.get_text().strip() for elem in genre_elements])
        
        return ''
    
    def extract_rating(self, soup):
        """استخراج امتیاز فیلم"""
        # جستجو برای امتیاز IMDB
        rating_patterns = [
            r'imdb.*?(\d+\.?\d*)',
            r'rating.*?(\d+\.?\d*)',
            r'امتیاز.*?(\d+\.?\d*)'
        ]
        
        text_content = soup.get_text()
        for pattern in rating_patterns:
            match = re.search(pattern, text_content, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return ''
    
    def extract_summary(self, soup):
        """استخراج خلاصه داستان"""
        # جستجو در تگ‌های مختلف برای خلاصه
        summary_selectors = [
            '.summary', '.description', '.plot',
            '[class*="summary"]', '[class*="description"]'
        ]
        
        for selector in summary_selectors:
            summary_elem = soup.select_one(selector)
            if summary_elem:
                return summary_elem.get_text().strip()
        
        # جستجو در پاراگراف‌ها
        paragraphs = soup.find_all('p')
        for p in paragraphs:
            text = p.get_text().strip()
            if len(text) > 100:  # خلاصه معمولاً طولانی است
                return text
        
        return ''
    
    def extract_poster(self, soup):
        """استخراج URL پوستر فیلم"""
        # جستجو در تگ video برای poster
        video_tag = soup.find('video')
        if video_tag and video_tag.get('poster'):
            return video_tag.get('poster')
        
        # جستجو در تصاویر با کلاس‌های مخصوص
        poster_selectors = [
            'img[class*="poster"]',
            'img[class*="cover"]',
            '.movie-poster img',
            '.poster img'
        ]
        
        for selector in poster_selectors:
            img = soup.select_one(selector)
            if img and img.get('src'):
                return img.get('src')
        
        return ''
    
    def process_all_movies(self):
        """پردازش تمام فیلم‌ها در پوشه"""
        for root, dirs, files in os.walk(self.base_path):
            for file in files:
                if file == 'index.html':
                    file_path = os.path.join(root, file)
                    movie_info = self.extract_movie_info(file_path)
                    if movie_info:
                        self.movies_data.append(movie_info)
                        print(f"پردازش شد: {movie_info['نام_فیلم']}")
    
    def save_to_csv(self, output_file='movies_data.csv'):
        """ذخیره اطلاعات در فایل CSV"""
        if not self.movies_data:
            print("هیچ اطلاعاتی برای ذخیره وجود ندارد!")
            return
        
        fieldnames = ['نام_فیلم', 'سال_انتشار', 'ژانر', 'امتیاز', 'خلاصه_داستان', 'پوستر']
        
        with open(output_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(self.movies_data)
        
        print(f"اطلاعات {len(self.movies_data)} فیلم در {output_file} ذخیره شد!")

# اجرای اسکریپت
if __name__ == "__main__":
    extractor = MovieExtractor()
    extractor.process_all_movies()
    extractor.save_to_csv('فیلم_ها.csv')