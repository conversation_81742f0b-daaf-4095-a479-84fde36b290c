#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

def analyze_results():
    """تحلیل نتایج نهایی"""
    try:
        df = pd.read_csv('series_episodes_online.csv')
        
        print('📊 آمار کلی:')
        print(f'کل قسمت‌ها: {len(df)}')
        print(f'تعداد سریال‌ها: {df["series_id"].nunique()}')
        print(f'تعداد فصل‌ها: {df["season_number"].nunique()}')
        print()
        
        print('🎵 انواع صدا:')
        print(df['audio_type'].value_counts())
        print()
        
        print('📺 فصل‌ها:')
        print(df['season_number'].value_counts().sort_index())
        print()
        
        print('🎬 نمونه سریال‌ها:')
        for series_id in df['series_id'].unique()[:3]:
            series_data = df[df['series_id'] == series_id]
            seasons = series_data['season_number'].unique()
            episodes_count = len(series_data)
            print(f'  سریال {series_id}: {len(seasons)} فصل، {episodes_count} قسمت')
            print(f'    فصل‌ها: {sorted(seasons)}')
        
    except Exception as e:
        print(f"خطا: {e}")

if __name__ == "__main__":
    analyze_results()
