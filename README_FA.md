# اسکرپر فیلم - نسخه پیشرفته

این اسکرپر برای استخراج اطلاعات فیلم‌ها از سایت myf2m.net طراحی شده است.

## ویژگی‌های جدید

### 🔄 اسکرپ نامحدود
- اسکرپ تمام صفحات سایت بدون محدودیت
- ادامه خودکار تا زمانی که صفحات خالی شوند
- توقف هوشمند بعد از 3 صفحه خالی متوالی

### 🚫 جلوگیری از تکرار
- ذخیره URL های پردازش شده در فایل `processed_urls.txt`
- عدم اسکرپ مجدد فیلم‌های قبلاً پردازش شده
- امکان ادامه کار بعد از توقف برنامه

### 📊 ساختار داده منظم
داده‌ها در 4 فایل CSV جداگانه ذخیره می‌شوند:

#### 1. `movies.csv` - اطلاعات اصلی فیلم‌ها
```csv
movie_id,title,year,rating,poster,url
17790d6fad8f,N.E.S.T.,2025,4.6,https://...,https://...
```

#### 2. `download_links.csv` - لینک‌های دانلود
```csv
movie_id,link_url,quality,type,button_text
17790d6fad8f,https://...,1080p WEB-DL,direct_download,دانلود مستقیم
```

#### 3. `genres_summaries.csv` - ژانر و خلاصه
```csv
movie_id,genre,summary
17790d6fad8f,ژانرها :هیجان انگیز,آشیانه، فیلمی مهیج...
```

#### 4. `processed_urls.txt` - URL های پردازش شده
```
https://www.myf2m.net/54682/n-e-s-t-2025/
https://www.myf2m.net/54663/vitoria-2025/
```

### 🔗 ارتباط داده‌ها
تمام فایل‌ها از طریق `movie_id` (شناسه یکتای 12 کاراکتری) به هم متصل هستند.

## نحوه استفاده

### اجرای برنامه
```bash
python web_scraper.py
```

### گزینه‌های موجود

#### 1. اسکرپ نامحدود
- اسکرپ تمام صفحات سایت
- ذخیره خودکار در فایل‌های CSV
- امکان توقف با Ctrl+C

#### 2. اسکرپ محدود (تست)
- اسکرپ تعداد محدودی صفحه
- مناسب برای تست

#### 3. نمایش آمار
- نمایش تعداد رکوردهای ذخیره شده
- بررسی وضعیت فایل‌ها

## مزایای ساختار جدید

### 🎯 کارایی بالا
- حجم کمتر فایل‌ها
- سرعت بیشتر در جستجو
- مدیریت بهتر حافظه

### 🔍 قابلیت جستجو
- جستجوی سریع بر اساس movie_id
- فیلتر کردن بر اساس کیفیت لینک‌ها
- دسته‌بندی بر اساس ژانر

### 📈 قابلیت گسترش
- اضافه کردن فیلدهای جدید آسان
- امکان ادغام با پایگاه داده
- سازگاری با ابزارهای تحلیل داده

## مثال استفاده از داده‌ها

### پیدا کردن تمام لینک‌های یک فیلم
```python
import pandas as pd

# خواندن فایل‌ها
movies = pd.read_csv('movies.csv')
links = pd.read_csv('download_links.csv')

# پیدا کردن فیلم مورد نظر
movie = movies[movies['title'].str.contains('N.E.S.T.')]
movie_id = movie['movie_id'].iloc[0]

# پیدا کردن لینک‌های دانلود
movie_links = links[links['movie_id'] == movie_id]
print(movie_links)
```

### فیلتر کردن بر اساس کیفیت
```python
# لینک‌های 1080p
hd_links = links[links['quality'].str.contains('1080p')]

# لینک‌های دانلود مستقیم
direct_links = links[links['type'] == 'direct_download']
```

## نکات مهم

### ⚠️ احترام به سرور
- تاخیر 1 ثانیه بین درخواست‌ها
- مدیریت خطاها
- عدم ارسال درخواست‌های مکرر

### 💾 مدیریت فایل‌ها
- فایل‌ها با encoding UTF-8-sig ذخیره می‌شوند
- سازگاری کامل با Excel
- امکان باز کردن در هر نرم‌افزار CSV

### 🔄 ادامه کار
- در صورت قطع برق یا خطا، برنامه از جایی که متوقف شده ادامه می‌دهد
- هیچ داده‌ای از دست نمی‌رود
- امکان اجرای چندین بار بدون نگرانی

## عیب‌یابی

### مشکلات رایج
1. **خطای اتصال**: بررسی اتصال اینترنت
2. **فایل قفل شده**: بستن Excel قبل از اجرا
3. **حافظه کم**: اجرای برنامه در بازه‌های زمانی کوتاه‌تر

### لاگ‌ها
برنامه تمام فعالیت‌ها را در کنسول نمایش می‌دهد:
- تعداد فیلم‌های پیدا شده
- وضعیت پردازش هر فیلم
- آمار کلی در پایان

## نسخه‌های آینده

### ویژگی‌های در نظر گرفته شده
- [ ] پشتیبانی از پروکسی
- [ ] اسکرپ موازی (چند thread)
- [ ] رابط گرافیکی
- [ ] صادرات به فرمت‌های مختلف
- [ ] ادغام با پایگاه داده

---

**توسعه‌دهنده**: اسکرپر فیلم پیشرفته  
**تاریخ**: 2025  
**نسخه**: 2.0
