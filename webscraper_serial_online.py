#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اسکریپت اسکرپ آنلاین سریال‌ها از سایت myf2m.com
نویسنده: AI Assistant
تاریخ: 2025-07-23
"""

import os
import re
import csv
import json
import time
import hashlib
import requests
from pathlib import Path
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse, quote
import logging
from datetime import datetime
import random

class OnlineSeriesScraper:
    """کلاس اصلی برای اسکرپ آنلاین سریال‌ها"""

    def __init__(self, base_url='https://www.myf2m.com'):
        """مقداردهی اولیه اسکریپت"""
        self.base_url = base_url
        self.series_list_url = f"{base_url}/series/"

        # تنظیم session
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'fa-IR,fa;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

        # فایل‌های CSV خروجی
        self.series_info_file = 'series_info_online.csv'
        self.series_episodes_file = 'series_episodes_online.csv'
        self.series_details_file = 'series_details_online.csv'
        self.processed_series_file = 'processed_series_online.json'
        self.scraped_urls_file = 'scraped_urls.json'

        # آمار پردازش
        self.stats = {
            'total_pages': 32,
            'current_page': 0,
            'total_series': 0,
            'processed_series': 0,
            'new_episodes': 0,
            'errors': 0,
            'network_errors': 0,
            'start_time': datetime.now()
        }

        # تنظیمات تاخیر
        self.min_delay = 1  # حداقل تاخیر بین درخواست‌ها (ثانیه)
        self.max_delay = 3  # حداکثر تاخیر
        self.page_delay = 2  # تاخیر بین صفحات

        # تنظیمات OMDb API (غیرفعال شده به دلیل محدودیت)
        self.omdb_enabled = False  # غیرفعال کردن OMDb
        self.omdb_api_key = '9b1ff0bb'
        self.omdb_base_url = 'http://www.omdbapi.com/'

        # تنظیمات TMDB API (رایگان و بدون محدودیت)
        self.tmdb_enabled = True
        self.tmdb_api_key = '8265bd1679663a7ea12ac168da84d2e8'  # کلید واقعی TMDB
        self.tmdb_base_url = 'https://api.themoviedb.org/3'

        # تنظیم لاگ
        self.setup_logging()

        # بارگذاری داده‌های قبلی
        self.processed_series = self.load_processed_series()
        self.scraped_urls = self.load_scraped_urls()

        # مقداردهی فایل‌های CSV
        self.initialize_csv_files()

    def setup_logging(self):
        """تنظیم سیستم لاگ‌گیری"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('series_scraper_online.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def load_processed_series(self):
        """بارگذاری لیست سریال‌های پردازش شده"""
        if os.path.exists(self.processed_series_file):
            try:
                with open(self.processed_series_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.logger.info(f"📂 بارگذاری {len(data)} سریال پردازش شده")
                    return data
            except Exception as e:
                self.logger.error(f"خطا در بارگذاری فایل پردازش شده: {e}")
        return {}

    def load_scraped_urls(self):
        """بارگذاری لیست URL های اسکرپ شده"""
        if os.path.exists(self.scraped_urls_file):
            try:
                with open(self.scraped_urls_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.logger.info(f"📂 بارگذاری {len(data)} URL اسکرپ شده")
                    return set(data)
            except Exception as e:
                self.logger.error(f"خطا در بارگذاری URL های اسکرپ شده: {e}")
        return set()

    def save_processed_series(self):
        """ذخیره لیست سریال‌های پردازش شده"""
        try:
            with open(self.processed_series_file, 'w', encoding='utf-8') as f:
                json.dump(self.processed_series, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"خطا در ذخیره فایل پردازش شده: {e}")

    def save_scraped_urls(self):
        """ذخیره لیست URL های اسکرپ شده"""
        try:
            with open(self.scraped_urls_file, 'w', encoding='utf-8') as f:
                json.dump(list(self.scraped_urls), f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"خطا در ذخیره URL های اسکرپ شده: {e}")

    def generate_series_id(self, series_name, url):
        """تولید ID یکتا برای سریال - نسخه بهبود یافته"""
        # تمیز کردن نام سریال
        clean_name = self.clean_series_name(series_name)

        # تمیز کردن URL
        clean_url = url.strip().lower()
        if clean_url.endswith('/'):
            clean_url = clean_url[:-1]

        unique_string = f"{clean_name}_{clean_url}"
        return hashlib.md5(unique_string.encode('utf-8')).hexdigest()[:12]

    def clean_series_name(self, name):
        """تمیز کردن نام سریال برای تولید ID ثابت"""
        if not name:
            return ""

        # تبدیل به حروف کوچک
        clean = name.lower().strip()

        # حذف کلمات اضافی
        remove_words = [
            'دانلود', 'سریال', 'انیمه', 'مستند', 'فصل', 'بدون', 'سانسور',
            'با', 'زیرنویس', 'فارسی', 'چسبیده', 'دوبله'
        ]

        for word in remove_words:
            clean = clean.replace(word, ' ')

        # تمیز کردن فاصله‌های اضافی
        clean = ' '.join(clean.split())

        # حذف کاراکترهای خاص
        import string
        clean = ''.join(c for c in clean if c.isalnum() or c.isspace())

        return clean.strip()

    def initialize_csv_files(self):
        """ایجاد فایل‌های CSV اگر وجود نداشته باشند"""

        # فایل اطلاعات پایه سریال‌ها
        if not os.path.exists(self.series_info_file):
            with open(self.series_info_file, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                writer.writerow([
                    'series_id', 'persian_name', 'english_name', 'release_date',
                    'rating', 'poster_url', 'url', 'date_added'
                ])

        # فایل قسمت‌ها و لینک‌های دانلود
        if not os.path.exists(self.series_episodes_file):
            with open(self.series_episodes_file, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                writer.writerow([
                    'series_id', 'season_number', 'episode_number', 'episode_title',
                    'download_link', 'quality', 'file_size', 'episode_date', 'audio_type'
                ])

        # فایل جزئیات سریال‌ها
        if not os.path.exists(self.series_details_file):
            with open(self.series_details_file, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                writer.writerow(['series_id', 'plot_summary', 'genres', 'total_seasons', 'total_episodes'])

    def make_request(self, url, retries=3):
        """درخواست HTTP با مدیریت خطا و تاخیر"""
        for attempt in range(retries):
            try:
                # تاخیر تصادفی بین درخواست‌ها
                delay = random.uniform(self.min_delay, self.max_delay)
                time.sleep(delay)

                response = self.session.get(url, timeout=30)
                response.raise_for_status()

                # بررسی محتوای صفحه
                if len(response.content) < 1000:
                    self.logger.warning(f"⚠️ محتوای کم برای URL: {url}")

                return response

            except requests.exceptions.RequestException as e:
                self.stats['network_errors'] += 1
                self.logger.error(f"خطای شبکه در تلاش {attempt + 1}: {e}")

                if attempt < retries - 1:
                    wait_time = (attempt + 1) * 5
                    self.logger.info(f"⏳ انتظار {wait_time} ثانیه قبل از تلاش مجدد...")
                    time.sleep(wait_time)
                else:
                    self.logger.error(f"❌ شکست در دریافت {url} پس از {retries} تلاش")
                    return None

    def extract_series_from_page(self, page_url):
        """استخراج لیست سریال‌ها از یک صفحه"""
        response = self.make_request(page_url)
        if not response:
            return []

        try:
            soup = BeautifulSoup(response.content, 'html.parser')
            series_list = []

            # جستجوی المان‌های سریال (بر اساس ساختار واقعی سایت)
            # هر سریال در یک div با لینک و تصویر قرار دارد
            series_containers = soup.find_all('div', style=lambda x: x and 'float:left' in x)

            if not series_containers:
                # جستجوی جایگزین
                series_containers = soup.find_all('div', class_='post-item')

            if not series_containers:
                # جستجوی عمومی‌تر
                series_links = soup.find_all('a', href=lambda x: x and '/series/' in x and x != '/series/')
                series_containers = []
                for link in series_links:
                    parent = link.parent
                    if parent and parent not in series_containers:
                        series_containers.append(parent)

            for item in series_containers:
                try:
                    # استخراج لینک سریال
                    link_elem = item.find('a', href=lambda x: x and '/series/' in x)
                    if not link_elem:
                        continue

                    series_url = urljoin(self.base_url, link_elem.get('href', ''))

                    # بررسی اینکه لینک معتبر است
                    if '/series/' not in series_url or series_url.endswith('/series/'):
                        continue

                    # استخراج نام سریال از title attribute یا متن لینک
                    series_name = link_elem.get('title', '').strip()
                    if not series_name:
                        series_name = link_elem.get_text(strip=True)
                    if not series_name:
                        # جستجو در المان‌های فرزند
                        title_elem = item.find('h1') or item.find('h2') or item.find('h3')
                        if title_elem:
                            series_name = title_elem.get_text(strip=True)

                    if not series_name or series_name == 'نامشخص':
                        continue

                    # استخراج پوستر
                    img_elem = item.find('img')
                    poster_url = ''
                    if img_elem:
                        poster_src = img_elem.get('src', '') or img_elem.get('data-src', '')
                        if poster_src:
                            poster_url = urljoin(self.base_url, poster_src)

                    # استخراج امتیاز از متن
                    rating = ''
                    item_text = item.get_text()
                    rating_match = re.search(r'(\d+\.?\d*)/10', item_text)
                    if rating_match:
                        rating = rating_match.group(1)

                    # استخراج سال انتشار
                    release_year = ''
                    year_match = re.search(r'\b(19|20)\d{2}\b', item_text)
                    if year_match:
                        release_year = year_match.group(0)

                    series_info = {
                        'name': series_name,
                        'url': series_url,
                        'poster_url': poster_url,
                        'rating': rating,
                        'release_year': release_year
                    }

                    series_list.append(series_info)

                except Exception as e:
                    self.logger.error(f"خطا در استخراج سریال: {e}")
                    continue

            self.logger.info(f"📺 {len(series_list)} سریال از صفحه استخراج شد")
            return series_list

        except Exception as e:
            self.logger.error(f"خطا در پردازش صفحه {page_url}: {e}")
            return []

    def extract_series_details(self, series_url):
        """استخراج جزئیات کامل یک سریال"""
        response = self.make_request(series_url)
        if not response:
            return None

        try:
            soup = BeautifulSoup(response.content, 'html.parser')

            # استخراج اطلاعات پایه
            title_elem = soup.find('h1') or soup.find('.title')
            persian_name = title_elem.get_text(strip=True) if title_elem else 'نامشخص'

            # استخراج نام انگلیسی
            english_name = ''
            english_elem = soup.find('.original-title') or soup.find('.english-title')
            if english_elem:
                english_name = english_elem.get_text(strip=True)

            # استخراج خلاصه داستان
            plot_summary = ''
            # جستجوی خلاصه داستان در پاراگراف‌های مختلف
            plot_candidates = soup.find_all('p')
            for p in plot_candidates:
                text = p.get_text(strip=True)
                if len(text) > 50 and ('سریال' in text or 'داستان' in text or len(text) > 100):
                    plot_summary = text
                    break

            # استخراج ژانرها - فقط ژانرهای مربوط به این سریال
            genres = []

            # جستجوی بخش ژانرها در صفحه
            genre_section = soup.find(string=lambda text: text and 'ژانرها' in text)
            if genre_section:
                # پیدا کردن والد این متن
                parent = genre_section.parent
                if parent:
                    # جستجوی لینک‌های ژانر در همین بخش
                    genre_links = parent.find_next_siblings() or parent.find_all('a', href=lambda x: x and '/genres/' in x)
                    for link in genre_links[:10]:  # محدود به 10 ژانر اول
                        if link.name == 'a' and '/genres/' in link.get('href', ''):
                            genre_text = link.get_text(strip=True)
                            if genre_text and genre_text not in genres and len(genre_text) < 30:
                                genres.append(genre_text)

            # اگر ژانری پیدا نشد، از روش دیگر استفاده کن
            if not genres:
                # جستجوی ژانرها در بخش مشخصات سریال
                # ابتدا بخش اطلاعات سریال را پیدا کن
                info_section = soup.find('div', class_=lambda x: x and 'info' in ' '.join(x) if isinstance(x, list) else 'info' in x)
                if info_section:
                    genre_links = info_section.find_all('a', href=lambda x: x and '/genres/' in x)
                    for link in genre_links:
                        genre_name = link.get_text(strip=True)
                        if genre_name and len(genre_name) > 1 and genre_name not in genres:
                            genres.append(genre_name)

                # اگر هنوز ژانری نیافت، از متن "ژانرها :" استفاده کن
                if not genres:
                    genre_text_elem = soup.find(string=lambda text: text and 'ژانرها' in text)
                    if genre_text_elem and genre_text_elem.parent:
                        # جستجوی لینک‌های ژانر در همان بخش
                        parent_section = genre_text_elem.parent.parent if genre_text_elem.parent.parent else genre_text_elem.parent
                        genre_links = parent_section.find_all('a', href=lambda x: x and '/genres/' in x)
                        for link in genre_links:
                            genre_name = link.get_text(strip=True)
                            if genre_name and len(genre_name) > 1 and genre_name not in genres:
                                genres.append(genre_name)

            # استخراج تعداد فصل‌ها و قسمت‌ها از TMDB API
            total_seasons = 0
            total_episodes = 0
            tmdb_info = None

            # جستجو در TMDB فقط برای تعداد فصل‌ها و قسمت‌ها
            if getattr(self, 'tmdb_enabled', True):
                search_title = english_name if english_name else persian_name
                self.logger.info(f"🔍 جستجوی TMDB برای تعداد فصل/قسمت: {search_title}")
                tmdb_search = self.search_series_tmdb(search_title)

                if tmdb_search:
                    tmdb_id = tmdb_search['tmdb_id']
                    self.logger.info(f"✅ TMDB ID یافت شد: {tmdb_id}")

                    # دریافت فقط تعداد فصل‌ها و قسمت‌ها
                    tmdb_info = self.get_series_details_tmdb(tmdb_id)

                    if tmdb_info:
                        total_seasons = tmdb_info['total_seasons']
                        total_episodes = tmdb_info['total_episodes']
                        self.logger.info(f"📊 از TMDB: {total_seasons} فصل، {total_episodes} قسمت")
                else:
                    self.logger.warning(f"❌ سریال در TMDB یافت نشد: {search_title}")

            # اگر TMDB و OMDb هر دو غیرفعال هستند، از قسمت‌های استخراج شده استفاده کن
            if not tmdb_info and not getattr(self, 'omdb_enabled', True):
                episodes_data = self.extract_episodes_and_links(series_url)
                if episodes_data:
                    # محاسبه تعداد فصل‌ها و قسمت‌ها از داده‌های واقعی
                    seasons_set = set()
                    episodes_set = set()

                    for episode in episodes_data:
                        season_num = episode.get('season_number', 1)
                        episode_num = episode.get('episode_number', 0)

                        seasons_set.add(season_num)
                        episodes_set.add(f"S{season_num}E{episode_num}")

                    total_seasons = len(seasons_set)
                    total_episodes = len(episodes_set)

                    self.logger.info(f"📊 از سایت: {total_seasons} فصل، {total_episodes} قسمت")

            # جستجوی IMDb ID با چندین روش (فقط اگر OMDb فعال باشد)
            search_titles = []

            # اضافه کردن نام انگلیسی اگر موجود است
            if english_name and len(english_name.strip()) > 2:
                search_titles.append(english_name)

            # اضافه کردن نام فارسی پاک شده
            if persian_name:
                clean_persian = self.clean_series_title(persian_name)
                if clean_persian and len(clean_persian.strip()) > 2 and clean_persian not in search_titles:
                    search_titles.append(clean_persian)

            # اضافه کردن نام استخراج شده از URL
            url_name = self.extract_name_from_url(series_url)
            if url_name and url_name not in search_titles:
                search_titles.append(url_name)

            # جستجوی IMDb ID
            imdb_id = None
            for search_title in search_titles:
                if search_title and len(search_title.strip()) > 2:
                    imdb_id = self.search_imdb_id(search_title)
                    if imdb_id:
                        break

            if imdb_id:
                omdb_info = self.get_series_info_from_omdb(imdb_id)

                if omdb_info:
                    total_seasons = omdb_info.get('total_seasons', 0)

                    # محاسبه تعداد کل قسمت‌ها از تمام فصل‌ها
                    if total_seasons > 0:
                        for season_num in range(1, total_seasons + 1):
                            season_episodes = self.get_season_episodes_from_omdb(imdb_id, season_num)
                            total_episodes += season_episodes
                            time.sleep(0.3)  # تاخیر کوتاه بین درخواست‌ها

                    self.logger.info(f"✅ داده‌های دقیق از OMDb: {total_seasons} فصل، {total_episodes} قسمت")
                else:
                    self.logger.warning(f"⚠️ خطا در دریافت اطلاعات از OMDb برای ID: {imdb_id}")
            else:
                self.logger.warning(f"❌ IMDb ID برای سریال یافت نشد. عناوین جستجو شده: {search_titles}")

            # اگر از OMDb اطلاعات نگرفت، داده‌ها را نامشخص قرار بده
            if total_seasons == 0 and total_episodes == 0:
                self.logger.warning("⚠️ اطلاعات معتبر یافت نشد - داده‌ها نامشخص تنظیم می‌شوند")
                total_seasons = "نامشخص"
                total_episodes = "نامشخص"

            # اطمینان از اینکه genres لیست است
            if isinstance(genres, str):
                genres_str = genres
            elif isinstance(genres, list):
                genres_str = ', '.join(genres) if genres else 'نامشخص'
            else:
                genres_str = str(genres) if genres else 'نامشخص'

            # استخراج قسمت‌ها و لینک‌های دانلود
            episodes = self.extract_episodes_and_links(series_url)

            print(f"خلاصه داستان: {plot_summary[:100]}...")
            print(f"ژانرها: {genres_str}")
            print(f"تعداد فصل‌ها: {total_seasons}")
            print(f"تعداد قسمت‌ها: {total_episodes}")
            print(f"قسمت‌های استخراج شده: {len(episodes) if episodes else 0}")

            return {
                'persian_name': persian_name,
                'english_name': english_name,
                'plot_summary': plot_summary,
                'genres': genres_str,
                'total_seasons': total_seasons,
                'total_episodes': total_episodes,
                'episodes': episodes or []
            }

        except Exception as e:
            self.logger.error(f"خطا در استخراج جزئیات سریال {series_url}: {e}")
            return None

    def extract_episodes_and_links(self, series_url):
        """استخراج قسمت‌ها و لینک‌های دانلود از تمام فصل‌ها با روش‌های بهبود یافته"""
        response = self.make_request(series_url)
        if not response:
            return []

        try:
            soup = BeautifulSoup(response.content, 'html.parser')
            all_episodes = []

            # روش 1: جستجوی لینک‌های مستقیم ویدیو (اولویت اول - اطلاعات کامل)
            episode_links = soup.find_all('a', href=lambda x: x and ('.mkv' in x or '.mp4' in x or '.avi' in x))
            if episode_links:
                episodes = self.extract_direct_episodes_improved(episode_links)
                all_episodes.extend(episodes)
                self.logger.info(f"📺 {len(episodes)} قسمت از لینک‌های مستقیم استخراج شد")

            # روش 2: جستجوی باکس‌های دانلود خاص (برای دوبله/زیرنویس جداگانه)
            download_episodes = self.extract_episodes_from_download_boxes_improved(soup)
            if download_episodes:
                # ترکیب با قسمت‌های موجود
                all_episodes.extend(download_episodes)
                self.logger.info(f"📺 {len(download_episodes)} قسمت اضافی از باکس‌های دانلود استخراج شد")

            # روش 3: جستجوی عناصر entry (آخرین گزینه)
            if not all_episodes:
                entry_episodes = self.extract_episodes_from_entries(soup)
                if entry_episodes:
                    all_episodes.extend(entry_episodes)
                    self.logger.info(f"📺 {len(entry_episodes)} قسمت از عناصر entry استخراج شد")

            # روش 4: جستجوی لینک‌های فصل‌های مختلف
            if not all_episodes:
                season_links = self.find_season_links(soup, series_url)

                for season_url in season_links:
                    if season_url != series_url:
                        season_episodes = self.extract_episodes_from_season_page(season_url)
                        if season_episodes:
                            all_episodes.extend(season_episodes)
                            self.logger.info(f"📺 {len(season_episodes)} قسمت از {season_url} استخراج شد")

            # حذف قسمت‌های تکراری
            if all_episodes:
                unique_episodes = self.remove_duplicate_episodes(all_episodes)
                self.logger.info(f"🔄 {len(all_episodes)} قسمت یافت شد، {len(unique_episodes)} قسمت یکتا")
                return unique_episodes
            else:
                self.logger.warning(f"⚠️ هیچ قسمتی در {series_url} یافت نشد")
                return []

        except Exception as e:
            self.logger.error(f"خطا در استخراج قسمت‌ها از {series_url}: {e}")
            return []

    def find_episode_boxes(self, soup):
        """یافتن باکس‌های حاوی اطلاعات قسمت"""
        episode_boxes = []

        # جستجوی div هایی که حاوی اطلاعات قسمت هستند
        content_divs = soup.find_all(['div', 'section', 'article'])

        for div in content_divs:
            text = div.get_text()
            # بررسی اینکه آیا این div حاوی اطلاعات قسمت است
            if ('قسمت' in text and 'فصل' in text and
                len(text.strip()) < 1000 and  # افزایش حد طول
                ('دانلود' in text or 'Download' in text or
                 'زیرنویس' in text or 'دوبله' in text or
                 div.find('a', href=True))):
                episode_boxes.append(div)

        return episode_boxes

    def extract_episodes_from_boxes(self, episode_boxes):
        """استخراج قسمت‌ها از باکس‌های یافت شده"""
        episodes = []

        for box in episode_boxes:
            try:
                text = box.get_text()

                # استخراج شماره فصل و قسمت
                season_number, episode_number = self.extract_season_and_episode(text)

                if episode_number == 0:
                    continue

                # تشخیص نوع قسمت (اصلی یا دوبله)
                is_dubbed = self.is_dubbed_version(text)

                # جستجوی لینک‌های دانلود در این باکس
                download_links = []
                links = box.find_all('a', href=True)

                for link in links:
                    href = link.get('href')
                    link_text = link.get_text().strip()

                    # بررسی اینکه آیا این لینک دانلود است
                    if (href and ('download' in href.lower() or
                                'دانلود' in link_text or
                                'Download' in link_text or
                                any(ext in href for ext in ['.mkv', '.mp4', '.avi']))):

                        # تشخیص نوع لینک از URL و متن لینک
                        link_is_dubbed = self.is_dubbed_version(link_text + ' ' + href)

                        # استخراج کیفیت و حجم
                        quality = self.extract_quality(link_text + ' ' + href)
                        file_size = self.extract_file_size(link_text + ' ' + href)

                        # اضافه کردن برچسب دوبله اگر لازم است
                        if link_is_dubbed:
                            quality = f"{quality} (دوبله فارسی)" if quality != 'نامشخص' else "دوبله فارسی"

                        # تعیین نوع لینک
                        link_type = 'دوبله فارسی' if link_is_dubbed else 'زیرنویس فارسی'

                        download_links.append({
                            'quality': quality,
                            'file_size': file_size,
                            'download_url': href,
                            'link_text': link_text,
                            'is_dubbed': link_is_dubbed,
                            'link_type': link_type
                        })

                # اگر لینک دانلود یافت نشد، یک لینک پیش‌فرض اضافه کن
                if not download_links:
                    # تشخیص نوع از متن کل باکس
                    box_is_dubbed = self.is_dubbed_version(text)
                    link_type = 'دوبله فارسی' if box_is_dubbed else 'زیرنویس فارسی'
                    download_links.append({
                        'quality': 'نامشخص',
                        'file_size': 'نامشخص',
                        'download_url': '#',
                        'link_text': 'لینک دانلود یافت نشد',
                        'is_dubbed': box_is_dubbed,
                        'link_type': link_type
                    })

                episodes.append({
                    'season_number': season_number,
                    'episode_number': episode_number,
                    'episode_title': f'قسمت {episode_number}',
                    'download_links': download_links,
                    'is_dubbed': is_dubbed
                })

            except Exception as e:
                self.logger.error(f"خطا در استخراج قسمت از باکس: {e}")
                continue

        return episodes

    def extract_episodes_from_entries(self, soup):
        """استخراج قسمت‌ها از عناصر entry (برای سریال‌هایی مثل The Bear)"""
        episodes = []

        try:
            # جستجوی عناصر با کلاس entry
            entry_elements = soup.find_all(['div', 'article'], class_='entry')

            for entry in entry_elements:
                text = entry.get_text()

                # بررسی اینکه آیا این entry حاوی اطلاعات قسمت است
                if 'قسمت' in text and 'فصل' in text:
                    # استخراج شماره فصل و قسمت
                    season_number, episode_number = self.extract_season_and_episode(text)

                    if episode_number == 0:
                        continue

                    # تشخیص نوع (دوبله یا زیرنویس)
                    is_dubbed = self.is_dubbed_version(text)
                    link_type = 'دوبله فارسی' if is_dubbed else 'زیرنویس فارسی'

                    # جستجوی لینک‌های دانلود در این entry
                    download_links = []
                    links = entry.find_all('a', href=True)

                    for link in links:
                        href = link.get('href', '')
                        link_text = link.get_text().strip()

                        # بررسی اینکه آیا این لینک دانلود است
                        if (href and ('download' in href.lower() or
                                    'دانلود' in link_text or
                                    any(ext in href for ext in ['.mkv', '.mp4', '.avi']) or
                                    'kingupload' in href or 'upload' in href)):

                            # استخراج کیفیت و حجم
                            quality = self.extract_quality(link_text + ' ' + href)
                            file_size = self.extract_file_size(link_text + ' ' + href)

                            download_links.append({
                                'quality': quality,
                                'file_size': file_size,
                                'download_url': href,
                                'link_text': link_text,
                                'is_dubbed': is_dubbed,
                                'link_type': link_type
                            })

                    # اگر لینک دانلود یافت نشد، یک لینک پیش‌فرض اضافه کن
                    if not download_links:
                        download_links.append({
                            'quality': 'نامشخص',
                            'file_size': 'نامشخص',
                            'download_url': '#',
                            'link_text': 'لینک دانلود یافت نشد',
                            'is_dubbed': is_dubbed,
                            'link_type': link_type
                        })

                    episodes.append({
                        'season_number': season_number,
                        'episode_number': episode_number,
                        'episode_title': f'قسمت {episode_number}',
                        'download_links': download_links,
                        'is_dubbed': is_dubbed
                    })

        except Exception as e:
            self.logger.error(f"خطا در استخراج قسمت‌ها از عناصر entry: {e}")

        return episodes

    def extract_episodes_from_download_boxes(self, soup):
        """استخراج قسمت‌ها از باکس‌های دانلود خاص بر اساس ساختار واقعی"""
        episodes = []

        try:
            # جستجوی باکس‌های دانلود با کلاس‌های شناسایی شده
            download_boxes = soup.find_all(['div'], class_=lambda x: x and
                                         any(cls in ' '.join(x) if isinstance(x, list) else cls in x
                                             for cls in ['download-list', 'download-season']))

            for box in download_boxes:
                # تشخیص نوع (دوبله یا زیرنویس) از کلاس
                box_classes = box.get('class', [])
                box_class_str = ' '.join(box_classes) if box_classes else ''

                # تشخیص دقیق‌تر نوع
                if 'dubbled' in box_class_str:
                    is_dubbed = True
                    link_type = 'دوبله فارسی'
                elif 'hardsub' in box_class_str:
                    is_dubbed = False
                    link_type = 'زیرنویس فارسی'
                else:
                    # تشخیص از محتوای متنی
                    box_text = box.get_text()
                    is_dubbed = self.is_dubbed_version(box_text)
                    link_type = 'دوبله فارسی' if is_dubbed else 'زیرنویس فارسی'

                # جستجوی لینک‌های قسمت در این باکس
                episode_links = box.find_all('a', href=lambda x: x and
                                            any(ext in x for ext in ['.mkv', '.mp4', '.avi']))

                for link in episode_links:
                    href = link.get('href', '')
                    link_text = link.get_text().strip()

                    # استخراج شماره فصل و قسمت از متن لینک
                    season_number, episode_number = self.extract_season_and_episode(link_text)

                    if episode_number == 0:
                        continue

                    # استخراج کیفیت و حجم
                    quality = self.extract_quality(link_text + ' ' + href)
                    file_size = self.extract_file_size(link_text + ' ' + href)

                    episodes.append({
                        'season_number': season_number,
                        'episode_number': episode_number,
                        'episode_title': f'قسمت {episode_number}',
                        'download_links': [{
                            'quality': quality,
                            'file_size': file_size,
                            'download_url': href,
                            'link_text': link_text,
                            'is_dubbed': is_dubbed,
                            'link_type': link_type
                        }],
                        'is_dubbed': is_dubbed
                    })

        except Exception as e:
            self.logger.error(f"خطا در استخراج قسمت‌ها از باکس‌های دانلود: {e}")

        return episodes

    def extract_episodes_from_download_boxes_improved(self, soup):
        """استخراج قسمت‌ها از باکس‌های دانلود - نسخه بهبود یافته"""
        episodes = []

        try:
            # جستجوی باکس‌های دانلود با کلاس‌های شناسایی شده
            download_boxes = soup.find_all(['div'], class_=lambda x: x and
                                         any(cls in ' '.join(x) if isinstance(x, list) else cls in x
                                             for cls in ['download-list', 'download-season']))

            for box in download_boxes:
                # تشخیص نوع (دوبله یا زیرنویس) از کلاس
                box_classes = box.get('class', [])
                box_class_str = ' '.join(box_classes) if box_classes else ''

                # تشخیص دقیق‌تر نوع
                if 'dubbled' in box_class_str:
                    is_dubbed = True
                    link_type = 'دوبله فارسی'
                elif 'hardsub' in box_class_str:
                    is_dubbed = False
                    link_type = 'زیرنویس فارسی'
                else:
                    # تشخیص از محتوای متنی
                    box_text = box.get_text()
                    is_dubbed = self.is_dubbed_version(box_text)
                    link_type = 'دوبله فارسی' if is_dubbed else 'زیرنویس فارسی'

                # جستجوی لینک‌های قسمت در این باکس
                episode_links = box.find_all('a', href=lambda x: x and
                                            any(ext in x for ext in ['.mkv', '.mp4', '.avi']))

                episode_dict = {}

                for link in episode_links:
                    href = link.get('href', '')
                    link_text = link.get_text().strip()

                    # استخراج فصل و قسمت از URL (اولویت اول)
                    season_number, episode_number = self.extract_season_and_episode_from_url(href)

                    # اگر از URL استخراج نشد، از متن استخراج کن
                    if season_number == 1 and episode_number == 0:
                        season_number, episode_number = self.extract_season_and_episode(link_text)

                    if episode_number == 0:
                        continue

                    # استخراج کیفیت و حجم
                    quality = self.extract_quality(link_text + ' ' + href)
                    file_size = self.extract_file_size(link_text + ' ' + href)

                    # ایجاد کلید یکتا برای قسمت
                    episode_key = f"S{season_number}E{episode_number}"

                    # اگر قسمت وجود ندارد، ایجاد کن
                    if episode_key not in episode_dict:
                        episode_dict[episode_key] = {
                            'season_number': season_number,
                            'episode_number': episode_number,
                            'episode_title': f"قسمت {episode_number:02d}",
                            'download_links': []
                        }

                    # اضافه کردن لینک دانلود
                    episode_dict[episode_key]['download_links'].append({
                        'download_url': href,
                        'quality': quality,
                        'file_size': file_size,
                        'link_type': link_type,
                        'link_text': link_text,
                        'is_dubbed': is_dubbed
                    })

                # اضافه کردن قسمت‌های این باکس
                episodes.extend(list(episode_dict.values()))

        except Exception as e:
            self.logger.error(f"خطا در استخراج قسمت‌ها از باکس‌های دانلود: {e}")

        return episodes

    def extract_direct_episodes_improved(self, episode_links):
        """استخراج قسمت‌ها از لینک‌های مستقیم - نسخه بهبود یافته"""
        episodes = []
        episode_dict = {}

        for link_elem in episode_links:
            try:
                download_url = link_elem.get('href', '')
                link_text = link_elem.get_text(strip=True)

                if not download_url or not link_text:
                    continue

                # استخراج فصل و قسمت از URL (اولویت اول)
                season_number, episode_number = self.extract_season_and_episode_from_url(download_url)

                # اگر از URL استخراج نشد، از متن استخراج کن
                if season_number == 1 and episode_number == 0:
                    season_number, episode_number = self.extract_season_and_episode(link_text)

                if episode_number == 0:
                    continue

                # تشخیص نوع صدا از URL و متن لینک
                full_text = link_text + ' ' + download_url
                is_dubbed = self.is_dubbed_version(full_text)
                link_type = 'دوبله فارسی' if is_dubbed else 'زیرنویس فارسی'

                # استخراج کیفیت و حجم
                quality = self.extract_quality(link_text + ' ' + download_url)
                file_size = self.extract_file_size(link_text + ' ' + download_url)

                # ایجاد کلید یکتا برای قسمت
                episode_key = f"S{season_number}E{episode_number}"

                # اگر قسمت وجود ندارد، ایجاد کن
                if episode_key not in episode_dict:
                    episode_dict[episode_key] = {
                        'season_number': season_number,
                        'episode_number': episode_number,
                        'episode_title': f"قسمت {episode_number:02d}",
                        'download_links': []
                    }

                # اضافه کردن لینک دانلود
                episode_dict[episode_key]['download_links'].append({
                    'download_url': download_url,
                    'quality': quality,
                    'file_size': file_size,
                    'link_type': link_type,
                    'link_text': link_text,
                    'is_dubbed': is_dubbed
                })

            except Exception as e:
                self.logger.error(f"خطا در استخراج لینک قسمت: {e}")
                continue

        # تبدیل دیکشنری به لیست
        episodes = list(episode_dict.values())

        # مرتب‌سازی بر اساس شماره قسمت
        episodes.sort(key=lambda x: (x['season_number'], x['episode_number']))

        return episodes

    def extract_season_and_episode_from_url(self, url):
        """استخراج فصل و قسمت از URL"""
        season_number = 1  # پیش‌فرض
        episode_number = 0

        # الگوهای مختلف در URL
        url_patterns = [
            r'/S(\d+)E(\d+)',  # /S01E01
            r'/S(\d+)/.*E(\d+)',  # /S01/...E01
            r'Season[._-](\d+).*Episode[._-](\d+)',  # Season.1.Episode.1
            r'S(\d+)\.E(\d+)',  # S01.E01
            r'\.S(\d+)E(\d+)\.',  # .S01E01.
            r'/S(\d+)/',  # فقط فصل
            r'Season[._-](\d+)',  # فقط فصل
        ]

        for pattern in url_patterns:
            match = re.search(pattern, url, re.IGNORECASE)
            if match:
                if len(match.groups()) >= 2:
                    season_number = int(match.group(1))
                    episode_number = int(match.group(2))
                    return season_number, episode_number
                elif len(match.groups()) == 1:
                    season_number = int(match.group(1))
                    # اگر فقط فصل یافت شد، سعی کن قسمت را از جای دیگر پیدا کنی
                    episode_patterns = [
                        r'E(\d+)',
                        r'Episode[._-](\d+)',
                        r'\.(\d+)\.',
                    ]
                    for ep_pattern in episode_patterns:
                        ep_match = re.search(ep_pattern, url, re.IGNORECASE)
                        if ep_match:
                            episode_number = int(ep_match.group(1))
                            return season_number, episode_number
                    break

        return season_number, episode_number

    def is_dubbed_version(self, text):
        """تشخیص اینکه آیا این نسخه دوبله فارسی است"""
        # کلمات کلیدی دوبله
        dubbed_keywords = [
            'دوبله فارسی',
            'دوبله فیلیمو',
            'دوبله',
            'Persian Dubbed',
            'Dubbed',
            'dubbled',  # از کلاس CSS
            'farsi.dubbed',
            'farsi_dubbed',
            'persian.dubbed',
            'persian_dubbed'
        ]

        # کلمات کلیدی زیرنویس (نشان‌دهنده نسخه اصلی)
        subtitle_keywords = [
            'زیرنویس فارسی',
            'زیرنویس',
            'Subtitle',
            'Sub',
            'چسبیده',
            'hardsub',  # از کلاس CSS
            'farsi.sub',
            'farsi_sub',
            'persian.sub',
            'persian_sub'
        ]

        text_lower = text.lower()

        # اگر کلمه دوبله وجود داشت، دوبله است
        for keyword in dubbed_keywords:
            if keyword.lower() in text_lower:
                return True

        # اگر فقط زیرنویس وجود داشت، دوبله نیست
        for keyword in subtitle_keywords:
            if keyword.lower() in text_lower:
                return False

        # اگر هیچ‌کدام نبود، پیش‌فرض: اصلی (زیرنویس)
        return False

    def remove_duplicate_episodes(self, episodes):
        """حذف قسمت‌های تکراری و ترکیب نسخه‌های مختلف (اصلی + دوبله)"""
        unique_episodes = {}

        for episode in episodes:
            season_num = episode['season_number']
            episode_num = episode['episode_number']
            key = f"S{season_num}E{episode_num}"

            if key not in unique_episodes:
                # اولین بار این قسمت را می‌بینیم
                unique_episodes[key] = episode.copy()
                # حذف فلگ is_dubbed از سطح قسمت (فقط در لینک‌ها نگه می‌داریم)
                if 'is_dubbed' in unique_episodes[key]:
                    del unique_episodes[key]['is_dubbed']
            else:
                # این قسمت قبلاً وجود داشته - لینک‌ها را ترکیب می‌کنیم
                existing_links = unique_episodes[key]['download_links']
                new_links = episode['download_links']

                # اضافه کردن لینک‌های جدید که تکراری نیستند
                for new_link in new_links:
                    is_duplicate = False
                    for existing_link in existing_links:
                        # بررسی تکراری بودن بر اساس URL یا کیفیت مشابه
                        if (existing_link['download_url'] == new_link['download_url'] or
                            (existing_link['quality'] == new_link['quality'] and
                             existing_link.get('is_dubbed', False) == new_link.get('is_dubbed', False))):
                            is_duplicate = True
                            break

                    if not is_duplicate:
                        existing_links.append(new_link)

                # مرتب‌سازی لینک‌ها: ابتدا نسخه اصلی، سپس دوبله
                unique_episodes[key]['download_links'].sort(
                    key=lambda x: (x.get('is_dubbed', False), x['quality'])
                )

        # تبدیل به لیست و مرتب‌سازی
        result = list(unique_episodes.values())
        result.sort(key=lambda x: (x['season_number'], x['episode_number']))

        return result

    def find_season_links(self, soup, base_url):
        """یافتن لینک‌های فصل‌های مختلف"""
        season_links = set([base_url])  # شامل صفحه اصلی

        try:
            # جستجوی لینک‌های فصل در صفحه
            season_patterns = [
                r'href="([^"]*season[^"]*)"',
                r'href="([^"]*فصل[^"]*)"',
                r'href="([^"]*s\d+[^"]*)"'
            ]

            page_html = str(soup)
            for pattern in season_patterns:
                matches = re.findall(pattern, page_html, re.IGNORECASE)
                for match in matches:
                    if match.startswith('http'):
                        season_links.add(match)
                    elif match.startswith('/'):
                        season_links.add(urljoin(base_url, match))

            # جستجوی لینک‌های فصل در منوها
            season_elements = soup.find_all(['a', 'div'], text=re.compile(r'فصل\s*\d+|Season\s*\d+|S\d+', re.IGNORECASE))
            for elem in season_elements:
                if elem.name == 'a' and elem.get('href'):
                    href = elem.get('href')
                    if href.startswith('http'):
                        season_links.add(href)
                    elif href.startswith('/'):
                        season_links.add(urljoin(base_url, href))

            return list(season_links)

        except Exception as e:
            self.logger.error(f"خطا در یافتن لینک‌های فصل: {e}")
            return [base_url]

    def extract_episodes_from_season_page(self, season_url):
        """استخراج قسمت‌ها از صفحه یک فصل خاص"""
        try:
            response = self.make_request(season_url)
            if not response:
                return []

            soup = BeautifulSoup(response.content, 'html.parser')
            episode_links = soup.find_all('a', href=lambda x: x and ('.mkv' in x or '.mp4' in x or '.avi' in x))

            if episode_links:
                return self.extract_direct_episodes(episode_links)

            return []

        except Exception as e:
            self.logger.error(f"خطا در استخراج قسمت‌ها از {season_url}: {e}")
            return []

    def extract_direct_episodes(self, episode_links):
        """استخراج قسمت‌ها از لینک‌های مستقیم"""
        episodes = []
        episode_dict = {}

        for link_elem in episode_links:
            try:
                download_url = link_elem.get('href', '')
                link_text = link_elem.get_text(strip=True)

                if not download_url or not link_text:
                    continue

                # استخراج شماره فصل و قسمت
                season_number, episode_number = self.extract_season_and_episode(link_text)
                if episode_number == 0:
                    continue

                # استخراج کیفیت
                quality = self.extract_quality(link_text)

                # استخراج حجم فایل
                file_size = self.extract_file_size(link_text)

                # تشخیص نوع صدا از URL و متن لینک
                full_text = link_text + ' ' + download_url
                is_dubbed = self.is_dubbed_version(full_text)
                link_type = 'دوبله فارسی' if is_dubbed else 'زیرنویس فارسی'

                # ایجاد کلید یکتا برای قسمت
                episode_key = f"S{season_number}E{episode_number}"

                # اگر قسمت وجود ندارد، ایجاد کن
                if episode_key not in episode_dict:
                    episode_dict[episode_key] = {
                        'season_number': season_number,
                        'episode_number': episode_number,
                        'episode_title': f"قسمت {episode_number:02d}",
                        'download_links': []
                    }

                # اضافه کردن لینک دانلود
                episode_dict[episode_key]['download_links'].append({
                    'download_url': download_url,
                    'quality': quality,
                    'file_size': file_size,
                    'link_type': link_type,
                    'link_text': link_text,
                    'is_dubbed': is_dubbed
                })

            except Exception as e:
                self.logger.error(f"خطا در استخراج لینک قسمت: {e}")
                continue

        # تبدیل دیکشنری به لیست
        episodes = list(episode_dict.values())

        # مرتب‌سازی بر اساس شماره قسمت
        episodes.sort(key=lambda x: (x['season_number'], x['episode_number']))

        return episodes

    def extract_episode_number(self, text):
        """استخراج شماره قسمت از متن"""
        # جستجوی الگوهای مختلف شماره قسمت
        patterns = [
            r'قسمت\s*(\d+)',
            r'E(\d+)',
            r'Episode\s*(\d+)',
            r'ep\s*(\d+)',
            r'\.E(\d+)\.',
            r'S\d+E(\d+)',
            r'(\d+)(?=\.mkv|\.mp4|\.avi)'
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return int(match.group(1))

        return 0

    def extract_season_and_episode(self, text):
        """استخراج شماره فصل و قسمت از متن - بهبود یافته"""
        season_number = 1  # پیش‌فرض
        episode_number = 0

        # الگوهای مختلف برای فصل و قسمت (ترتیب مهم است)
        patterns = [
            (r'فصل\s*(\d+).*قسمت\s*(\d+)', 'season_first'),  # فصل 2 قسمت 5
            (r'قسمت\s*(\d+).*فصل\s*(\d+)', 'episode_first'),  # قسمت 5 فصل 2
            (r'S(\d+)E(\d+)', 'season_first'),  # S02E05
            (r'Season\s*(\d+).*Episode\s*(\d+)', 'season_first'),  # Season 2 Episode 5
            (r'S(\d+)\.E(\d+)', 'season_first'),  # S02.E05
            (r'(\d+)x(\d+)', 'season_first'),  # 2x05
        ]

        for pattern, order in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                if order == 'episode_first':
                    # ترتیب: قسمت اول، فصل دوم
                    episode_number = int(match.group(1))
                    season_number = int(match.group(2))
                else:
                    # ترتیب: فصل اول، قسمت دوم
                    season_number = int(match.group(1))
                    episode_number = int(match.group(2))
                return season_number, episode_number

        # اگر فقط شماره قسمت یافت شد
        episode_number = self.extract_episode_number(text)

        # تلاش برای یافتن شماره فصل جداگانه
        season_patterns = [
            r'فصل\s*(\d+)',
            r'Season\s*(\d+)',
            r'S(\d+)(?!E)',  # S2 اما نه S2E1
        ]

        for pattern in season_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                season_number = int(match.group(1))
                break

        return season_number, episode_number



    def extract_download_links(self, episode_url):
        """استخراج لینک‌های دانلود از صفحه قسمت (اختیاری)"""
        # این متد دیگر استفاده نمی‌شود چون لینک‌ها مستقیماً در صفحه اصلی هستند
        return []

    def search_imdb_id(self, series_title):
        """جستجوی IMDb ID بر اساس عنوان سریال با چندین تلاش"""
        try:
            # لیست عناوین مختلف برای جستجو
            search_titles = []

            # عنوان پاک شده اصلی
            clean_title = self.clean_series_title(series_title)
            if clean_title and len(clean_title.strip()) > 2:
                search_titles.append(clean_title)

            # اگر عنوان اصلی شامل کلمات انگلیسی است
            english_match = re.search(r'([A-Za-z][A-Za-z\s:&\'-]+[A-Za-z])', series_title)
            if english_match:
                english_title = english_match.group(1).strip()
                if english_title not in search_titles:
                    search_titles.append(english_title)

            # حذف کلمات اضافی بیشتر
            for title in search_titles.copy():
                # حذف "season X" و "فصل X"
                clean_ver = re.sub(r'\s+(season|فصل)\s*\d+', '', title, flags=re.IGNORECASE).strip()
                if clean_ver and clean_ver not in search_titles:
                    search_titles.append(clean_ver)

            # جستجو با هر عنوان (فقط اگر OMDb فعال باشد)
            if not getattr(self, 'omdb_enabled', True):
                self.logger.info("⚠️ OMDb غیرفعال است - از اطلاعات سایت استفاده می‌شود")
            else:
                for search_title in search_titles:
                    if len(search_title.strip()) < 3:
                        continue

                    self.logger.info(f"🔍 جستجوی IMDb برای: '{search_title}'")

                    search_url = f"{self.omdb_base_url}?s={quote(search_title)}&type=series&apikey={self.omdb_api_key}"
                    response = requests.get(search_url, timeout=10)

                    if response.status_code == 200:
                        data = response.json()
                        if data.get('Response') == 'True' and 'Search' in data:
                            # بررسی نتایج برای بهترین تطابق
                            for result in data['Search'][:3]:  # بررسی 3 نتیجه اول
                                result_title = result.get('Title', '').lower()
                                search_lower = search_title.lower()

                                # بررسی تطابق نام
                                if (search_lower in result_title or
                                    result_title in search_lower or
                                    self.calculate_similarity(search_lower, result_title) > 0.7):

                                    imdb_id = result.get('imdbID')
                                    self.logger.info(f"✅ IMDb ID یافت شد: {imdb_id} برای '{result.get('Title')}'")
                                    return imdb_id
                        else:
                            # اگر خطای محدودیت API باشد، OMDb را غیرفعال کن
                            if 'limit' in data.get('Error', '').lower():
                                self.omdb_enabled = False
                                self.logger.warning("⚠️ محدودیت OMDb API - غیرفعال شد")
                                break

                    time.sleep(0.5)  # تاخیر بین جستجوها

            self.logger.warning(f"❌ IMDb ID برای '{series_title}' یافت نشد")
            return None

        except Exception as e:
            self.logger.error(f"خطا در جستجوی IMDb ID برای {series_title}: {e}")
            return None

    def calculate_similarity(self, str1, str2):
        """محاسبه شباهت بین دو رشته"""
        try:
            # حذف کاراکترهای اضافی
            str1 = re.sub(r'[^\w\s]', '', str1.lower())
            str2 = re.sub(r'[^\w\s]', '', str2.lower())

            # تقسیم به کلمات
            words1 = set(str1.split())
            words2 = set(str2.split())

            if not words1 or not words2:
                return 0

            # محاسبه تطابق کلمات
            intersection = words1.intersection(words2)
            union = words1.union(words2)

            return len(intersection) / len(union) if union else 0

        except:
            return 0

    def extract_name_from_url(self, url):
        """استخراج نام سریال از URL با بهبود دقت"""
        try:
            # استخراج بخش آخر URL
            url_parts = url.rstrip('/').split('/')
            if url_parts:
                last_part = url_parts[-1]

                # حذف کلمات اضافی فارسی و انگلیسی
                name = last_part.replace('-', ' ')

                # حذف کلمات رایج
                remove_patterns = [
                    r'season\s*\d+',
                    r'فصل\s*\d+',
                    r'series',
                    r'سریال',
                    r'download',
                    r'دانلود',
                    r'episode',
                    r'قسمت',
                    r'with\s+subtitle',
                    r'با\s+زیرنویس',
                    r'uncensored',
                    r'بدون\s+سانسور'
                ]

                for pattern in remove_patterns:
                    name = re.sub(pattern, '', name, flags=re.IGNORECASE)

                # پاک کردن فاصله‌های اضافی
                name = ' '.join(name.split())

                if len(name.strip()) > 2:
                    # تبدیل به Title Case
                    return ' '.join(word.capitalize() for word in name.split())

            return None

        except Exception as e:
            self.logger.error(f"خطا در استخراج نام از URL {url}: {e}")
            return None

    def clean_series_title(self, title):
        """پاک کردن کلمات اضافی از عنوان سریال با دقت بالا"""
        if not title:
            return ""

        original_title = title

        # حذف کلمات فارسی اضافی
        persian_patterns = [
            r'دانلود\s+',
            r'سریال\s+',
            r'انیمه\s+',
            r'مستند\s+',
            r'فصل\s+\d+\s*',
            r'قسمت\s+\d+\s*',
            r'بدون\s+سانسور.*',
            r'با\s+زیرنویس.*',
            r'زیرنویس\s+فارسی.*',
            r'چسبیده.*'
        ]

        for pattern in persian_patterns:
            title = re.sub(pattern, '', title, flags=re.IGNORECASE)

        # استخراج عنوان انگلیسی (بهبود یافته)
        english_patterns = [
            r'([A-Za-z][A-Za-z\s:&\'-\.]+[A-Za-z])',  # عنوان کامل انگلیسی
            r'([A-Z][a-z]+(?:\s+[A-Z][a-z]*)*)',      # کلمات با حروف بزرگ
            r'([A-Za-z]+(?:\s+[A-Za-z]+)*)'           # هر کلمه انگلیسی
        ]

        for pattern in english_patterns:
            english_match = re.search(pattern, title)
            if english_match:
                english_title = english_match.group(1).strip()
                # بررسی طول و کیفیت عنوان
                if len(english_title) >= 3 and not english_title.lower() in ['the', 'and', 'or', 'of', 'in', 'on', 'at']:
                    return english_title

        # اگر عنوان انگلیسی نیافت، عنوان پاک شده را برگردان
        cleaned = ' '.join(title.split())
        return cleaned if len(cleaned) >= 3 else original_title.strip()

    def get_series_info_from_omdb(self, imdb_id):
        """دریافت اطلاعات سریال از OMDb API"""
        # بررسی فعال بودن OMDb
        if not getattr(self, 'omdb_enabled', True):
            return None

        try:
            url = f"{self.omdb_base_url}?i={imdb_id}&apikey={self.omdb_api_key}"
            response = requests.get(url, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if data.get('Response') == 'True':
                    return {
                        'total_seasons': int(data.get('totalSeasons', 0)),
                        'imdb_rating': data.get('imdbRating', 'N/A'),
                        'genre': data.get('Genre', ''),
                        'year': data.get('Year', ''),
                        'plot': data.get('Plot', '')
                    }
                else:
                    # اگر خطای محدودیت API باشد، OMDb را غیرفعال کن
                    if 'limit' in data.get('Error', '').lower():
                        self.omdb_enabled = False
                        self.logger.warning("⚠️ محدودیت OMDb API - غیرفعال شد")

            return None

        except Exception as e:
            self.logger.error(f"خطا در دریافت اطلاعات از OMDb برای {imdb_id}: {e}")
            return None

    def get_season_episodes_from_omdb(self, imdb_id, season_number):
        """دریافت تعداد قسمت‌های یک فصل از OMDb API"""
        # بررسی فعال بودن OMDb
        if not getattr(self, 'omdb_enabled', True):
            return 0

        try:
            url = f"{self.omdb_base_url}?i={imdb_id}&Season={season_number}&apikey={self.omdb_api_key}"
            response = requests.get(url, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if data.get('Response') == 'True' and 'Episodes' in data:
                    return len(data['Episodes'])
                else:
                    # اگر خطای محدودیت API باشد، OMDb را غیرفعال کن
                    if 'limit' in data.get('Error', '').lower():
                        self.omdb_enabled = False
                        self.logger.warning("⚠️ محدودیت OMDb API - غیرفعال شد")

            return 0

        except Exception as e:
            self.logger.error(f"خطا در دریافت قسمت‌های فصل {season_number} از OMDb: {e}")
            return 0

    def search_series_tmdb(self, series_title):
        """جستجوی سریال در TMDB"""
        if not getattr(self, 'tmdb_enabled', True):
            return None

        try:
            # تمیز کردن عنوان برای جستجو
            clean_title = self.extract_english_name(series_title)

            search_url = f"{self.tmdb_base_url}/search/tv"
            params = {
                'api_key': self.tmdb_api_key,
                'query': clean_title,
                'language': 'en-US'
            }

            response = requests.get(search_url, params=params, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if data.get('results'):
                    # برگرداندن بهترین نتیجه
                    best_match = data['results'][0]
                    return {
                        'tmdb_id': best_match.get('id'),
                        'name': best_match.get('name'),
                        'first_air_date': best_match.get('first_air_date'),
                        'vote_average': best_match.get('vote_average'),
                        'overview': best_match.get('overview'),
                        'genre_ids': best_match.get('genre_ids', [])
                    }

            return None

        except Exception as e:
            self.logger.error(f"خطا در جستجوی TMDB برای {series_title}: {e}")
            return None

    def get_series_details_tmdb(self, tmdb_id):
        """دریافت جزئیات سریال از TMDB"""
        if not getattr(self, 'tmdb_enabled', True):
            return None

        try:
            detail_url = f"{self.tmdb_base_url}/tv/{tmdb_id}"
            params = {
                'api_key': self.tmdb_api_key,
                'language': 'en-US'
            }

            response = requests.get(detail_url, params=params, timeout=10)

            if response.status_code == 200:
                data = response.json()

                # استخراج ژانرها
                genres = [genre['name'] for genre in data.get('genres', [])]

                return {
                    'total_seasons': data.get('number_of_seasons', 0),
                    'total_episodes': data.get('number_of_episodes', 0),
                    'vote_average': data.get('vote_average', 0),
                    'first_air_date': data.get('first_air_date', ''),
                    'last_air_date': data.get('last_air_date', ''),
                    'overview': data.get('overview', ''),
                    'genres': genres,
                    'status': data.get('status', ''),
                    'seasons': data.get('seasons', [])
                }

            return None

        except Exception as e:
            self.logger.error(f"خطا در دریافت جزئیات TMDB برای {tmdb_id}: {e}")
            return None

    def get_season_details_tmdb(self, tmdb_id, season_number):
        """دریافت جزئیات فصل از TMDB"""
        if not getattr(self, 'tmdb_enabled', True):
            return None

        try:
            season_url = f"{self.tmdb_base_url}/tv/{tmdb_id}/season/{season_number}"
            params = {
                'api_key': self.tmdb_api_key,
                'language': 'en-US'
            }

            response = requests.get(season_url, params=params, timeout=10)

            if response.status_code == 200:
                data = response.json()
                return {
                    'episode_count': len(data.get('episodes', [])),
                    'air_date': data.get('air_date', ''),
                    'episodes': data.get('episodes', [])
                }

            return None

        except Exception as e:
            self.logger.error(f"خطا در دریافت جزئیات فصل {season_number} از TMDB: {e}")
            return None

    def check_for_new_episodes(self, series_id, series_url):
        """بررسی قسمت‌های جدید برای سریال"""
        try:
            # استخراج قسمت‌های فعلی از سایت
            current_episodes = self.extract_episodes_and_links(series_url)

            if not current_episodes:
                return 0

            # شمارش قسمت‌های فعلی در پایگاه داده
            existing_episodes = 0
            if os.path.exists(self.series_episodes_file):
                with open(self.series_episodes_file, 'r', encoding='utf-8-sig') as f:
                    reader = csv.reader(f)
                    next(reader)  # رد کردن header
                    for row in reader:
                        if row and row[0] == series_id:
                            existing_episodes += 1

            # محاسبه تعداد قسمت‌های جدید (قسمت‌های یکتا، نه لینک‌ها)
            unique_episodes = set()
            for episode in current_episodes:
                episode_key = f"S{episode['season_number']}E{episode['episode_number']}"
                unique_episodes.add(episode_key)
            total_current_episodes = len(unique_episodes)
            new_episodes_count = total_current_episodes - existing_episodes

            if new_episodes_count > 0:
                # اضافه کردن قسمت‌های جدید
                self.save_episodes(series_id, current_episodes)
                self.stats['new_episodes'] += new_episodes_count

                # بروزرسانی اطلاعات سریال در فایل پردازش شده
                self.processed_series[series_id]['last_update'] = datetime.now().isoformat()
                self.processed_series[series_id]['episode_count'] = total_current_episodes

            return new_episodes_count

        except Exception as e:
            self.logger.error(f"خطا در بررسی قسمت‌های جدید برای {series_url}: {e}")
            return 0

    def extract_quality(self, text):
        """استخراج کیفیت از متن"""
        quality_patterns = [
            r'(\d+p)',
            r'(720p|1080p|480p|360p|4K|2K)',
            r'(HD|FHD|UHD|SD)',
            r'(BluRay|BRRip|DVDRip|WEBRip|HDTV)'
        ]

        for pattern in quality_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)

        return 'نامشخص'

    def extract_file_size(self, text):
        """استخراج حجم فایل از متن"""
        size_pattern = r'(\d+(?:\.\d+)?\s*(?:GB|MB|KB|گیگابایت|مگابایت))'
        match = re.search(size_pattern, text, re.IGNORECASE)
        return match.group(1) if match else ''

    def detect_link_type(self, url):
        """تشخیص نوع لینک دانلود"""
        if 'direct' in url.lower():
            return 'مستقیم'
        elif any(host in url.lower() for host in ['mega', 'mediafire', 'drive.google']):
            return 'فایل هاست'
        elif 'torrent' in url.lower() or url.endswith('.torrent'):
            return 'تورنت'
        else:
            return 'نامشخص'

    def save_series_info(self, series_info, series_details):
        """ذخیره اطلاعات پایه سریال"""
        try:
            series_id = self.generate_series_id(series_info['name'], series_info['url'])

            with open(self.series_info_file, 'a', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                writer.writerow([
                    series_id,
                    series_details.get('persian_name', series_info['name']),
                    series_details.get('english_name', ''),
                    series_info.get('release_year', ''),
                    series_info.get('rating', ''),
                    series_info.get('poster_url', ''),
                    series_info['url'],
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                ])

            return series_id

        except Exception as e:
            self.logger.error(f"خطا در ذخیره اطلاعات سریال: {e}")
            return None

    def save_series_details(self, series_id, series_details):
        """ذخیره جزئیات سریال (خلاصه داستان و ژانر)"""
        try:
            with open(self.series_details_file, 'a', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                writer.writerow([
                    series_id,
                    series_details.get('plot_summary', ''),
                    series_details.get('genres', ''),
                    series_details.get('total_seasons', 0),
                    series_details.get('total_episodes', 0)
                ])

        except Exception as e:
            self.logger.error(f"خطا در ذخیره جزئیات سریال: {e}")

    def save_episodes(self, series_id, episodes, check_duplicates=True):
        """ذخیره قسمت‌ها و لینک‌های دانلود"""
        try:
            episode_count = 0
            existing_episodes = set()

            # اگر بررسی تکراری فعال باشد، قسمت‌های موجود را بخوان
            if check_duplicates and os.path.exists(self.series_episodes_file):
                with open(self.series_episodes_file, 'r', encoding='utf-8-sig') as f:
                    reader = csv.reader(f)
                    next(reader)  # رد کردن header
                    for row in reader:
                        if row and len(row) >= 5 and row[0] == series_id:
                            # ایجاد کلید یکتا برای هر قسمت
                            episode_key = f"{row[1]}_{row[2]}_{row[4]}"  # season_episode_link
                            existing_episodes.add(episode_key)

            with open(self.series_episodes_file, 'a', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)

                for episode in episodes:
                    if episode['download_links']:
                        for link_info in episode['download_links']:
                            # بررسی تکراری بودن
                            download_url = link_info.get('download_url', link_info.get('download_link', ''))
                            episode_key = f"{episode['season_number']}_{episode['episode_number']}_{download_url}"

                            if not check_duplicates or episode_key not in existing_episodes:
                                writer.writerow([
                                    series_id,
                                    episode['season_number'],
                                    episode['episode_number'],
                                    episode['episode_title'],
                                    download_url,
                                    link_info.get('quality', 'نامشخص'),
                                    link_info.get('file_size', ''),
                                    datetime.now().strftime('%Y-%m-%d'),
                                    link_info.get('link_type', 'نامشخص')
                                ])
                                episode_count += 1
                                existing_episodes.add(episode_key)
                    else:
                        # حتی اگر لینک دانلود نداشت، قسمت را ثبت کن
                        episode_key = f"{episode['season_number']}_{episode['episode_number']}_"

                        if not check_duplicates or episode_key not in existing_episodes:
                            # تعیین نوع لینک بر اساس اطلاعات قسمت
                            link_type = 'دوبله فارسی' if episode.get('is_dubbed', False) else 'زیرنویس فارسی'

                            writer.writerow([
                                series_id,
                                episode['season_number'],
                                episode['episode_number'],
                                episode['episode_title'],
                                '',  # download_link
                                '',  # quality
                                '',  # file_size
                                datetime.now().strftime('%Y-%m-%d'),
                                f'بدون لینک ({link_type})'
                            ])
                            episode_count += 1
                            existing_episodes.add(episode_key)

            return episode_count

        except Exception as e:
            self.logger.error(f"خطا در ذخیره قسمت‌ها: {e}")
            return 0

    def process_single_series(self, series_info):
        """پردازش یک سریال"""
        series_url = series_info['url']
        series_name = series_info['name']

        # بررسی اینکه آیا قبلاً پردازش شده
        series_id = self.generate_series_id(series_name, series_url)
        if series_id in self.processed_series:
            if hasattr(self, 'check_updates') and self.check_updates:
                # بررسی قسمت‌های جدید
                new_episodes = self.check_for_new_episodes(series_id, series_url)
                if new_episodes > 0:
                    self.logger.info(f"🆕 {new_episodes} قسمت جدید برای سریال {series_name} یافت شد")
                    return  # قسمت‌های جدید اضافه شد، ادامه نده
                else:
                    self.logger.info(f"⏭️ سریال {series_name} قبلاً پردازش شده و قسمت جدیدی ندارد")
                    return
            else:
                self.logger.info(f"⏭️ سریال {series_name} قبلاً پردازش شده")
                return

        self.logger.info(f"🎬 شروع پردازش سریال: {series_name}")

        try:
            # استخراج جزئیات سریال
            series_details = self.extract_series_details(series_url)
            if not series_details:
                self.logger.warning(f"⚠️ نتوانستم جزئیات سریال {series_name} را استخراج کنم")
                series_details = {'persian_name': series_name}

            # ذخیره اطلاعات پایه
            saved_series_id = self.save_series_info(series_info, series_details)
            if not saved_series_id:
                self.logger.error(f"❌ خطا در ذخیره اطلاعات سریال {series_name}")
                return

            # ذخیره جزئیات
            self.save_series_details(saved_series_id, series_details)

            # استخراج و ذخیره قسمت‌ها
            episodes = self.extract_episodes_and_links(series_url)
            episode_count = self.save_episodes(saved_series_id, episodes)

            # ثبت در لیست پردازش شده
            self.processed_series[series_id] = {
                'name': series_name,
                'url': series_url,
                'processed_date': datetime.now().isoformat(),
                'episode_count': episode_count
            }

            self.stats['processed_series'] += 1
            self.logger.info(f"✅ سریال {series_name} با {episode_count} قسمت پردازش شد")

        except Exception as e:
            self.stats['errors'] += 1
            self.logger.error(f"❌ خطا در پردازش سریال {series_name}: {e}")

    def print_progress(self):
        """نمایش پیشرفت کار"""
        elapsed = datetime.now() - self.stats['start_time']

        print(f"\n{'='*60}")
        print(f"📊 گزارش پیشرفت اسکرپ")
        print(f"{'='*60}")
        print(f"📄 صفحه فعلی: {self.stats['current_page']}/{self.stats['total_pages']}")
        print(f"🎬 کل سریال‌ها: {self.stats['total_series']}")
        print(f"✅ پردازش شده: {self.stats['processed_series']}")
        print(f"📺 قسمت‌های جدید: {self.stats['new_episodes']}")
        print(f"❌ خطاها: {self.stats['errors']}")
        print(f"🌐 خطاهای شبکه: {self.stats['network_errors']}")
        print(f"⏱️ زمان سپری شده: {elapsed}")
        print(f"{'='*60}\n")

    def run_scraper(self, start_page=1, end_page=None, check_updates=True):
        """اجرای اصلی اسکریپت"""
        if end_page is None:
            end_page = self.stats['total_pages']

        self.check_updates = check_updates
        self.logger.info(f"🚀 شروع اسکرپ آنلاین از صفحه {start_page} تا {end_page}")
        if check_updates:
            self.logger.info("🔄 بررسی قسمت‌های جدید فعال است")
        else:
            self.logger.info("⏭️ سریال‌های پردازش شده رد می‌شوند")

        try:
            for page_num in range(start_page, end_page + 1):
                self.stats['current_page'] = page_num

                # ساخت URL صفحه
                if page_num == 1:
                    page_url = self.series_list_url
                else:
                    page_url = f"{self.series_list_url}page/{page_num}/"

                self.logger.info(f"📄 پردازش صفحه {page_num}: {page_url}")

                # استخراج سریال‌ها از صفحه
                series_list = self.extract_series_from_page(page_url)

                if not series_list:
                    self.logger.warning(f"⚠️ هیچ سریالی در صفحه {page_num} یافت نشد")
                    continue

                self.stats['total_series'] += len(series_list)

                # پردازش هر سریال
                for series_info in series_list:
                    self.process_single_series(series_info)

                    # ذخیره پیشرفت هر 5 سریال
                    if self.stats['processed_series'] % 5 == 0:
                        self.save_processed_series()
                        self.save_scraped_urls()

                # نمایش پیشرفت هر 3 صفحه
                if page_num % 3 == 0:
                    self.print_progress()

                # تاخیر بین صفحات
                if page_num < end_page:
                    self.logger.info(f"⏳ انتظار {self.page_delay} ثانیه قبل از صفحه بعدی...")
                    time.sleep(self.page_delay)

        except KeyboardInterrupt:
            self.logger.info("⏹️ اسکرپ توسط کاربر متوقف شد")
        except Exception as e:
            self.logger.error(f"❌ خطای کلی در اسکرپ: {e}")
        finally:
            # ذخیره نهایی داده‌ها
            self.save_processed_series()
            self.save_scraped_urls()
            self.print_final_report()

    def print_final_report(self):
        """گزارش نهایی"""
        elapsed = datetime.now() - self.stats['start_time']

        print(f"\n{'='*70}")
        print(f"🎯 گزارش نهایی اسکرپ آنلاین سریال‌ها")
        print(f"{'='*70}")
        print(f"📄 صفحات پردازش شده: {self.stats['current_page']}/{self.stats['total_pages']}")
        print(f"🎬 کل سریال‌های یافت شده: {self.stats['total_series']}")
        print(f"✅ سریال‌های پردازش شده: {self.stats['processed_series']}")
        print(f"📺 کل قسمت‌های جدید: {self.stats['new_episodes']}")
        print(f"❌ تعداد خطاها: {self.stats['errors']}")
        print(f"🌐 خطاهای شبکه: {self.stats['network_errors']}")
        print(f"⏱️ کل زمان اجرا: {elapsed}")
        print(f"📁 فایل‌های خروجی:")
        print(f"   • {self.series_info_file}")
        print(f"   • {self.series_episodes_file}")
        print(f"   • {self.series_details_file}")
        print(f"   • {self.processed_series_file}")
        print(f"   • {self.scraped_urls_file}")
        print(f"{'='*70}")

        # محاسبه آمار عملکرد
        if elapsed.total_seconds() > 0:
            series_per_minute = (self.stats['processed_series'] * 60) / elapsed.total_seconds()
            print(f"📈 سرعت پردازش: {series_per_minute:.1f} سریال در دقیقه")

        success_rate = (self.stats['processed_series'] / max(self.stats['total_series'], 1)) * 100
        print(f"📊 نرخ موفقیت: {success_rate:.1f}%")
        print(f"{'='*70}\n")


def main():
    """تابع اصلی"""
    print("🎬 اسکریپت اسکرپ آنلاین سریال‌ها از myf2m.com")
    print("=" * 50)

    try:
        # ایجاد نمونه اسکریپت
        scraper = OnlineSeriesScraper()

        # دریافت تنظیمات از کاربر
        print("\n⚙️ تنظیمات اسکرپ:")

        start_page = input("📄 صفحه شروع (پیش‌فرض: 1): ").strip()
        start_page = int(start_page) if start_page.isdigit() else 1

        end_page = input("📄 صفحه پایان (پیش‌فرض: 32): ").strip()
        end_page = int(end_page) if end_page.isdigit() else 32

        check_updates = input("🔄 بررسی قسمت‌های جدید برای سریال‌های پردازش شده؟ (y/n، پیش‌فرض: y): ").strip().lower()
        check_updates = check_updates != 'n'

        if check_updates:
            print("✅ بررسی قسمت‌های جدید فعال است")
        else:
            print("⏭️ سریال‌های پردازش شده رد می‌شوند")

        # اجرای اسکرپ
        scraper.run_scraper(start_page, end_page, check_updates)

    except Exception as e:
        print(f"❌ خطای کلی: {e}")
        logging.error(f"خطای کلی در main: {e}")


if __name__ == "__main__":
    main()